<?php
/**
 * Database Setup Script
 * JOKIPRO.ID - Game Boosting Services
 * 
 * Run this script to setup the database and tables
 */

// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "jokipro_db";

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>JOKIPRO.ID Database Setup</h2>";
    echo "<p>Setting up database and tables...</p>";
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
    echo "<p>✓ Database '$database' created/verified</p>";
    
    // Use the database
    $pdo->exec("USE $database");
    
    // Read and execute the database schema
    $schema_file = 'database/jokipro_database.sql';
    if (file_exists($schema_file)) {
        $schema_sql = file_get_contents($schema_file);
        
        // Remove the CREATE DATABASE and USE statements since we already did that
        $schema_sql = preg_replace('/CREATE DATABASE.*?;/i', '', $schema_sql);
        $schema_sql = preg_replace('/USE.*?;/i', '', $schema_sql);
        
        // Split by semicolon and execute each statement
        $statements = array_filter(array_map('trim', explode(';', $schema_sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Ignore errors for existing tables/functions
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<p>⚠ Warning: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        echo "<p>✓ Database schema executed</p>";
    } else {
        echo "<p>❌ Schema file not found: $schema_file</p>";
    }
    
    // Insert sample data
    $sample_file = 'database/sample_data.sql';
    if (file_exists($sample_file)) {
        $sample_sql = file_get_contents($sample_file);
        
        // Split by semicolon and execute each statement
        $statements = array_filter(array_map('trim', explode(';', $sample_sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Ignore duplicate entry errors
                    if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                        echo "<p>⚠ Warning: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        echo "<p>✓ Sample data inserted</p>";
    } else {
        echo "<p>⚠ Sample data file not found: $sample_file</p>";
    }
    
    // Test the setup
    echo "<h3>Testing Database Setup</h3>";
    
    // Check tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>✓ Tables created: " . implode(', ', $tables) . "</p>";
    
    // Check users table
    $user_count = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "<p>✓ Users table: $user_count records</p>";
    
    // Check games table
    $game_count = $pdo->query("SELECT COUNT(*) FROM games")->fetchColumn();
    echo "<p>✓ Games table: $game_count records</p>";
    
    // Check services table
    $service_count = $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn();
    echo "<p>✓ Services table: $service_count records</p>";
    
    echo "<h3>✅ Database Setup Complete!</h3>";
    echo "<p>You can now use the login system and other features.</p>";
    echo "<p><strong>Test Accounts:</strong></p>";
    echo "<ul>";
    echo "<li>Email: <EMAIL> | Password: password</li>";
    echo "<li>Email: <EMAIL> | Password: password</li>";
    echo "<li>Email: <EMAIL> | Password: password</li>";
    echo "</ul>";
    echo "<p><a href='login.html'>Go to Login Page</a> | <a href='register.html'>Go to Register Page</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Database Setup Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}
?>
