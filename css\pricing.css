/* Pricing Page Styles - Professional Client-Ready Version */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.page-header p {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Header Stats */
.header-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
    color: white;
}

/* Pricing Section */
.pricing-section {
    padding: 80px 0;
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

.pricing-info {
    text-align: center;
    margin-bottom: 4rem;
}

.pricing-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a2e;
    margin-bottom: 1rem;
}

.pricing-info p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto 3rem;
    line-height: 1.6;
}

/* Pricing Grid */
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Pricing Cards */
.pricing-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 215, 0, 0.2);
    position: relative;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
    border-radius: 20px 20px 0 0;
}

.pricing-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.03) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.pricing-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 25px rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
}

.pricing-card:hover::after {
    opacity: 1;
}

/* Featured Card */
.pricing-card.featured {
    border: 2px solid #ffd700;
    background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
    position: relative;
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.15), 0 8px 20px rgba(0, 0, 0, 0.1);
}

.pricing-card.featured:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 30px 60px rgba(255, 215, 0, 0.2), 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* Popular Badge */
.popular-badge {
    position: absolute;
    top: -12px;
    right: 25px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
    border: 2px solid #fff;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.card-header {
    text-align: center;
    margin-bottom: 2rem;
}

.service-icon {
    margin-bottom: 1.5rem;
}

.service-icon img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #ffd700;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-card:hover .service-icon img {
    transform: scale(1.15) rotate(8deg);
    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.6);
}

.card-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a2e;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.game-subtitle {
    font-size: 0.85rem;
    color: #666;
    font-weight: 500;
}

/* Price Range */
.price-range {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #fff9e6 0%, #fffef7 100%);
    border-radius: 16px;
    border: 3px solid #ffd700;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.price-range::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.price-from {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.price-amount {
    font-size: 1.8rem;
    font-weight: 800;
    color: #ffd700;
    text-shadow: 0 3px 6px rgba(255, 215, 0, 0.4);
    position: relative;
    z-index: 1;
}

/* Features */
.features {
    margin-bottom: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin: 0.75rem 0;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.feature-item:hover {
    background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
    transform: translateX(5px) scale(1.02);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.3);
}

.feature-item i {
    color: #ffd700;
    font-size: 1rem;
    margin-right: 0.75rem;
    width: 16px;
    text-align: center;
}

.feature-item span {
    color: #333;
    font-size: 0.85rem;
    font-weight: 500;
    line-height: 1.3;
}

/* Order Button */
.btn-order-service {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    width: 100%;
    padding: 18px 32px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    text-decoration: none;
    border-radius: 20px;
    font-weight: 700;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    font-size: 1.1rem;
    box-shadow: 0 12px 30px rgba(255, 215, 0, 0.4);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-order-service::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-order-service:hover::before {
    left: 100%;
}

.btn-order-service:hover {
    background: linear-gradient(135deg, #ffed4e, #ffd700);
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 18px 40px rgba(255, 215, 0, 0.5);
    color: #1a1a2e;
    text-decoration: none;
}

.btn-order-service i {
    font-size: 1.1rem;
}

/* Payment Methods Section */
.payment-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
    border-top: 1px solid rgba(255, 215, 0, 0.2);
}

.payment-section h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a2e;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.payment-section p {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
}

.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.payment-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.payment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
}

.payment-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
}

.payment-item img {
    width: 60px;
    height: 60px;
    object-fit: contain;
    margin-bottom: 1rem;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.payment-item:hover img {
    transform: scale(1.1);
}

.payment-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.payment-item:hover .payment-icon {
    transform: scale(1.1);
}

.payment-item span {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    text-align: center;
}

.payment-info {
    max-width: 600px;
    margin: 0 auto;
}

.payment-note {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-left: 5px solid #ffd700;
}

.payment-note h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1a1a2e;
    margin-bottom: 1rem;
}

.payment-note ul {
    list-style: none;
    padding: 0;
}

.payment-note li {
    padding: 0.5rem 0;
    color: #555;
    font-size: 1rem;
    position: relative;
    padding-left: 1.5rem;
}

.payment-note li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #ffd700;
    font-weight: bold;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a2e;
    margin-bottom: 3rem;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-5px);
}

.faq-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1a1a2e;
    margin-bottom: 1rem;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .pricing-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        padding: 0 2rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 80px 0 50px;
    }

    .page-header h1 {
        font-size: 2.2rem;
    }

    .page-header p {
        font-size: 1rem;
        padding: 0 1rem;
    }

    .header-stats {
        gap: 1.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .pricing-section {
        padding: 50px 0;
    }

    .pricing-info h2 {
        font-size: 1.8rem;
    }

    .pricing-info p {
        padding: 0 1rem;
    }

    .pricing-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, auto);
        gap: 0.8rem;
        padding: 0 0.5rem;
        max-width: 100%;
        width: 100%;
    }

    .pricing-card {
        padding: 1rem;
        margin: 0;
        width: 100%;
        min-height: auto;
    }

    /* Mobile specific adjustments for better side-by-side layout */
    .service-icon img {
        width: 50px;
        height: 50px;
    }

    .card-header h3 {
        font-size: 1.1rem;
        margin-bottom: 0.3rem;
    }

    .game-subtitle {
        font-size: 0.75rem;
        margin-bottom: 1rem;
    }

    .price-range {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .price-amount {
        font-size: 1.4rem;
    }

    .feature-item {
        padding: 0.5rem;
        margin: 0.4rem 0;
    }

    .feature-item span {
        font-size: 0.75rem;
    }

    .btn-order-service {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    /* Payment section mobile */
    .payment-section {
        padding: 50px 0;
    }

    .payment-section h2 {
        font-size: 2rem;
        padding: 0 1rem;
    }

    .payment-methods {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 0 1rem;
    }

    .payment-item {
        padding: 1.5rem;
    }

    .payment-item img {
        width: 50px;
        height: 50px;
    }

    .payment-note {
        margin: 0 1rem;
        padding: 1.5rem;
    }

    .faq-section {
        padding: 50px 0;
    }

    .faq-section h2 {
        font-size: 1.8rem;
    }

    .faq-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 70px 0 40px;
    }

    .page-header h1 {
        font-size: 1.8rem;
        padding: 0 0.5rem;
    }

    .page-header p {
        font-size: 0.95rem;
        padding: 0 1rem;
    }

    .header-stats {
        flex-direction: row;
        gap: 1rem;
        justify-content: space-around;
        flex-wrap: wrap;
    }

    .stat-item {
        flex: 1;
        min-width: 80px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .pricing-section {
        padding: 40px 0;
    }

    .pricing-info h2 {
        font-size: 1.6rem;
        padding: 0 0.5rem;
    }

    .pricing-info p {
        font-size: 0.95rem;
        padding: 0 1rem;
    }

    .pricing-grid {
        gap: 1rem;
        padding: 0 0.5rem;
    }

    .pricing-card {
        padding: 1.25rem;
        max-width: 100%;
        margin: 0;
    }

    .service-icon img {
        width: 60px;
        height: 60px;
    }

    .card-header h3 {
        font-size: 1.2rem;
    }

    .game-subtitle {
        font-size: 0.8rem;
    }

    .price-range {
        padding: 0.5rem;
        margin-bottom: 1rem;
    }

    .price-amount {
        font-size: 1.2rem;
    }

    .feature-item {
        padding: 0.4rem;
        margin: 0.4rem 0;
    }

    .feature-item i {
        font-size: 0.9rem;
        margin-right: 0.5rem;
        width: 14px;
    }

    .feature-item span {
        font-size: 0.8rem;
    }

    .btn-order-service {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .faq-section {
        padding: 40px 0;
    }

    .faq-section h2 {
        font-size: 1.6rem;
        padding: 0 0.5rem;
    }

    .faq-grid {
        padding: 0 0.5rem;
    }

    .faq-item {
        padding: 1.25rem;
    }

    .faq-item h3 {
        font-size: 1rem;
    }

    .faq-item p {
        font-size: 0.85rem;
    }
}

/* Additional Visual Effects */
.pricing-card {
    animation: fadeInUp 0.6s ease-out;
}

.pricing-card:nth-child(1) { animation-delay: 0.1s; }
.pricing-card:nth-child(2) { animation-delay: 0.2s; }
.pricing-card:nth-child(3) { animation-delay: 0.3s; }
.pricing-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Floating Animation for Featured Card */
.pricing-card.featured {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Extra Small Mobile Devices */
@media (max-width: 480px) {
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 0.5rem;
    }

    .pricing-card {
        padding: 1rem;
        margin: 0;
    }

    .service-icon img {
        width: 60px;
        height: 60px;
    }

    .card-header h3 {
        font-size: 1.2rem;
    }

    .price-amount {
        font-size: 1.5rem;
    }

    .btn-order-service {
        padding: 14px 24px;
        font-size: 1rem;
    }
}
