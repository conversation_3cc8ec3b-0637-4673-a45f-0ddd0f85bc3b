<?php
/**
 * User Login API Endpoint
 * JOKIPRO.ID - Game Boosting Services
 */

require_once '../config/database.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo ApiResponse::error('Method not allowed', 405);
    exit();
}

try {
    // Get JSON input
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);
    
    if (!$data) {
        echo ApiResponse::badRequest('Invalid JSON data');
        exit();
    }
    
    // Validate required fields
    $email = Validator::required($data['email'] ?? '', 'email');
    $password = Validator::required($data['password'] ?? '', 'password');
    
    // Validate email format
    $email = Validator::email($email);
    
    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Check if user exists
    $query = "SELECT id, name, email, password, phone, whatsapp, google_id, facebook_id, 
                     avatar_url, email_verified, phone_verified, is_active, last_login 
              FROM users WHERE email = :email AND is_active = 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();
    
    $user = $stmt->fetch();
    
    if (!$user) {
        echo ApiResponse::unauthorized('Invalid email or password');
        exit();
    }
    
    // Check password (only if user has password - not for social login only users)
    if ($user['password'] && !Security::verifyPassword($password, $user['password'])) {
        echo ApiResponse::unauthorized('Invalid email or password');
        exit();
    } elseif (!$user['password']) {
        echo ApiResponse::badRequest('This account uses social login. Please use Google or Facebook login.');
        exit();
    }
    
    // Generate session token
    $session_token = Security::generateToken(64);
    $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
    
    // Get user device info
    $device_info = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    
    // Create session
    $session_query = "INSERT INTO user_sessions (user_id, session_token, device_info, ip_address, expires_at) 
                      VALUES (:user_id, :session_token, :device_info, :ip_address, :expires_at)";
    
    $session_stmt = $conn->prepare($session_query);
    $session_stmt->bindParam(':user_id', $user['id']);
    $session_stmt->bindParam(':session_token', $session_token);
    $session_stmt->bindParam(':device_info', $device_info);
    $session_stmt->bindParam(':ip_address', $ip_address);
    $session_stmt->bindParam(':expires_at', $expires_at);
    $session_stmt->execute();
    
    // Update last login
    $update_query = "UPDATE users SET last_login = NOW() WHERE id = :user_id";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bindParam(':user_id', $user['id']);
    $update_stmt->execute();
    
    // Prepare response data (exclude sensitive information)
    $response_data = [
        'user' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'phone' => $user['phone'],
            'whatsapp' => $user['whatsapp'],
            'avatar_url' => $user['avatar_url'],
            'email_verified' => (bool)$user['email_verified'],
            'phone_verified' => (bool)$user['phone_verified'],
            'has_social_login' => !empty($user['google_id']) || !empty($user['facebook_id'])
        ],
        'session' => [
            'token' => $session_token,
            'expires_at' => $expires_at
        ]
    ];
    
    echo ApiResponse::success($response_data, 'Login successful');
    
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    echo ApiResponse::badRequest($e->getMessage());
}
?>
