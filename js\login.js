/**
 * Login Page JavaScript
 * JOKIPRO.ID - Game Boosting Services
 */

// Initialize login page
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    
    if (loginForm) {
        loginForm.addEventListener('submit', handleEmailLogin);
    }
    
    // Check if user came from order page
    const urlParams = new URLSearchParams(window.location.search);
    const returnUrl = urlParams.get('return');
    
    if (returnUrl) {
        sessionStorage.setItem('returnUrl', returnUrl);
    }
});

/**
 * Handle email login
 */
async function handleEmailLogin(e) {
    e.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const remember = document.getElementById('remember').checked;

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Masuk...';
    submitBtn.disabled = true;

    try {
        // Call login API
        const response = await fetch('api/auth/login.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        });

        const result = await response.json();

        if (result.success) {
            // Store user data and session
            const userData = {
                ...result.data.user,
                loginMethod: 'email',
                loginTime: new Date().toISOString()
            };

            // Store user data and session token
            if (remember) {
                localStorage.setItem('userData', JSON.stringify(userData));
                localStorage.setItem('sessionToken', result.data.session.token);
            } else {
                sessionStorage.setItem('userData', JSON.stringify(userData));
                sessionStorage.setItem('sessionToken', result.data.session.token);
            }

            showNotification('Login berhasil! Mengalihkan...', 'success');

            // Redirect to return URL or order page
            setTimeout(() => {
                const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
                sessionStorage.removeItem('returnUrl');
                window.location.href = returnUrl;
            }, 1500);
        } else {
            showNotification(result.message || 'Login gagal', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showNotification('Terjadi kesalahan saat login', 'error');
    } finally {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

/**
 * Login with Google
 */
function loginWithGoogle() {
    showNotification('Mengalihkan ke Google...', 'info');
    
    // Simulate Google login
    setTimeout(() => {
        const userData = {
            email: '<EMAIL>',
            name: 'Google User',
            loginMethod: 'google',
            loginTime: new Date().toISOString()
        };
        
        sessionStorage.setItem('userData', JSON.stringify(userData));
        showNotification('Login dengan Google berhasil!', 'success');
        
        setTimeout(() => {
            const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
            sessionStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
        }, 1500);
    }, 1000);
}

/**
 * Login with Facebook
 */
function loginWithFacebook() {
    showNotification('Mengalihkan ke Facebook...', 'info');
    
    // Simulate Facebook login
    setTimeout(() => {
        const userData = {
            email: '<EMAIL>',
            name: 'Facebook User',
            loginMethod: 'facebook',
            loginTime: new Date().toISOString()
        };
        
        sessionStorage.setItem('userData', JSON.stringify(userData));
        showNotification('Login dengan Facebook berhasil!', 'success');
        
        setTimeout(() => {
            const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
            sessionStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
        }, 1500);
    }, 1000);
}

/**
 * Login with Discord
 */
function loginWithDiscord() {
    showNotification('Mengalihkan ke Discord...', 'info');
    
    // Simulate Discord login
    setTimeout(() => {
        const userData = {
            email: '<EMAIL>',
            name: 'Discord User',
            loginMethod: 'discord',
            loginTime: new Date().toISOString()
        };
        
        sessionStorage.setItem('userData', JSON.stringify(userData));
        showNotification('Login dengan Discord berhasil!', 'success');
        
        setTimeout(() => {
            const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
            sessionStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
        }, 1500);
    }, 1000);
}

/**
 * Continue as guest
 */
function continueAsGuest() {
    const userData = {
        email: '<EMAIL>',
        name: 'Guest User',
        loginMethod: 'guest',
        loginTime: new Date().toISOString()
    };
    
    sessionStorage.setItem('userData', JSON.stringify(userData));
    showNotification('Melanjutkan sebagai guest...', 'info');
    
    setTimeout(() => {
        const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
        sessionStorage.removeItem('returnUrl');
        window.location.href = returnUrl;
    }, 1000);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Style notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

/**
 * Get notification icon
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

/**
 * Get notification color
 */
function getNotificationColor(type) {
    switch (type) {
        case 'success': return '#28a745';
        case 'error': return '#dc3545';
        case 'warning': return '#ffc107';
        default: return '#17a2b8';
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
`;
document.head.appendChild(style);
