/* Contact Page Styles */

/* <PERSON> Header */
.page-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 3rem;
}

/* Quick Contact Buttons */
.quick-contact {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.quick-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    min-width: 120px;
}

.quick-btn:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.quick-btn.whatsapp:hover {
    background: rgba(37, 211, 102, 0.2);
    border: 1px solid #25d366;
}

.quick-btn.email:hover {
    background: rgba(234, 67, 53, 0.2);
    border: 1px solid #ea4335;
}

.quick-btn.chat:hover {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid #ffd700;
}

.quick-btn i {
    font-size: 2rem;
}

.quick-btn span {
    font-weight: 600;
    font-size: 1rem;
}

.quick-btn small {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Contact Methods */
.contact-methods {
    padding: 80px 0;
    background: #f8f9fa;
}

.methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.method-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.method-card.primary {
    border-color: #25d366;
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.2);
}

.method-icon {
    margin-bottom: 1.5rem;
}

.method-icon i {
    font-size: 3rem;
    color: #ffd700;
}

.method-card.primary .method-icon i {
    color: #25d366;
}

.method-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.method-desc {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.contact-details {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
}

.contact-details p {
    margin-bottom: 0.5rem;
    color: #555;
    font-size: 0.9rem;
}

.contact-details strong {
    color: #333;
}

.btn-contact {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    background: #ffd700;
    color: #333;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-contact:hover {
    background: #ffed4e;
    transform: translateY(-2px);
}

.method-card.primary .btn-contact {
    background: #25d366;
    color: white;
}

.method-card.primary .btn-contact:hover {
    background: #128c7e;
}

/* Support Hours */
.support-hours {
    padding: 80px 0;
    background: white;
}

.support-hours h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 3rem;
}

.hours-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.hours-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.hours-card:hover {
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hours-icon {
    margin-bottom: 1rem;
}

.hours-icon i {
    font-size: 2rem;
    color: #ffd700;
}

.hours-card h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.hours-card p {
    color: #666;
    margin-bottom: 1rem;
}

.status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status.online {
    background: #28a745;
    color: white;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.faq-section h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 3rem;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: 10px;
    margin-bottom: 1rem;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-question h3 {
    font-size: 1.1rem;
    color: #333;
    margin: 0;
}

.faq-question i {
    color: #ffd700;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 300px;
}

.faq-answer p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Contact Form */
.contact-form-section {
    padding: 80px 0;
    background: white;
}

.form-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.form-container h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1rem;
}

.form-container p {
    color: #666;
    margin-bottom: 3rem;
}

.contact-form {
    text-align: left;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.btn-submit {
    width: 100%;
    padding: 15px;
    background: #ffd700;
    color: #333;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-submit:hover {
    background: #ffed4e;
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2rem;
    }
    
    .quick-contact {
        flex-direction: column;
        align-items: center;
    }
    
    .quick-btn {
        width: 200px;
    }
    
    .methods-grid {
        grid-template-columns: 1fr;
    }
    
    .hours-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hours-grid {
        grid-template-columns: 1fr;
    }
}
