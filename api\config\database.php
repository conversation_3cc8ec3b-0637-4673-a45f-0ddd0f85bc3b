<?php
/**
 * Database Configuration for JOKIPRO.ID
 * Game Boosting Services Database Connection
 */

class Database {
    // Database credentials
    private $host = "localhost";
    private $db_name = "jokipro_db";
    private $username = "root";  // Change this for production
    private $password = "";      // Change this for production
    private $charset = "utf8mb4";
    
    public $conn;
    
    /**
     * Get database connection
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed");
        }
        
        return $this->conn;
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            return $conn !== null;
        } catch(Exception $e) {
            return false;
        }
    }
    
    /**
     * Get database info
     */
    public function getDatabaseInfo() {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->query("SELECT VERSION() as version");
            $result = $stmt->fetch();
            
            return [
                'status' => 'connected',
                'host' => $this->host,
                'database' => $this->db_name,
                'version' => $result['version']
            ];
        } catch(Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }
}

/**
 * Response helper class
 */
class ApiResponse {
    
    public static function success($data = null, $message = "Success", $code = 200) {
        http_response_code($code);
        return json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    public static function error($message = "Error", $code = 400, $details = null) {
        http_response_code($code);
        return json_encode([
            'success' => false,
            'message' => $message,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    public static function notFound($message = "Resource not found") {
        return self::error($message, 404);
    }
    
    public static function serverError($message = "Internal server error") {
        return self::error($message, 500);
    }
    
    public static function unauthorized($message = "Unauthorized access") {
        return self::error($message, 401);
    }
    
    public static function badRequest($message = "Bad request", $details = null) {
        return self::error($message, 400, $details);
    }
}

/**
 * Input validation helper
 */
class Validator {
    
    public static function required($value, $field_name) {
        if (empty($value) || trim($value) === '') {
            throw new Exception("Field '{$field_name}' is required");
        }
        return trim($value);
    }
    
    public static function email($email) {
        $email = trim($email);
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Invalid email format");
        }
        return $email;
    }
    
    public static function phone($phone) {
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        if (!preg_match('/^(\+62|62|0)[0-9]{9,13}$/', $phone)) {
            throw new Exception("Invalid phone number format");
        }
        return $phone;
    }
    
    public static function numeric($value, $field_name) {
        if (!is_numeric($value)) {
            throw new Exception("Field '{$field_name}' must be numeric");
        }
        return (float)$value;
    }
    
    public static function integer($value, $field_name) {
        if (!filter_var($value, FILTER_VALIDATE_INT)) {
            throw new Exception("Field '{$field_name}' must be an integer");
        }
        return (int)$value;
    }
    
    public static function maxLength($value, $max_length, $field_name) {
        if (strlen($value) > $max_length) {
            throw new Exception("Field '{$field_name}' cannot exceed {$max_length} characters");
        }
        return $value;
    }
    
    public static function minLength($value, $min_length, $field_name) {
        if (strlen($value) < $min_length) {
            throw new Exception("Field '{$field_name}' must be at least {$min_length} characters");
        }
        return $value;
    }
}

/**
 * Security helper
 */
class Security {
    
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }
    
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    public static function encryptSensitiveData($data, $key = null) {
        if ($key === null) {
            $key = 'jokipro_secret_key_2024'; // Change this in production
        }
        return base64_encode(openssl_encrypt($data, 'AES-256-CBC', $key, 0, substr(hash('sha256', $key), 0, 16)));
    }
    
    public static function decryptSensitiveData($encrypted_data, $key = null) {
        if ($key === null) {
            $key = 'jokipro_secret_key_2024'; // Change this in production
        }
        return openssl_decrypt(base64_decode($encrypted_data), 'AES-256-CBC', $key, 0, substr(hash('sha256', $key), 0, 16));
    }
}

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}
?>
