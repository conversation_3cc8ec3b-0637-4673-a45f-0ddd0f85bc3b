<?php
/**
 * User Registration API Endpoint
 * JOKIPRO.ID - Game Boosting Services
 */

require_once '../config/database.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo ApiResponse::error('Method not allowed', 405);
    exit();
}

try {
    // Get JSON input
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);
    
    if (!$data) {
        echo ApiResponse::badRequest('Invalid JSON data');
        exit();
    }
    
    // Validate required fields
    $name = Validator::required($data['name'] ?? '', 'name');
    $email = Validator::required($data['email'] ?? '', 'email');
    $password = Validator::required($data['password'] ?? '', 'password');
    $phone = Validator::required($data['phone'] ?? '', 'phone');
    
    // Validate formats
    $email = Validator::email($email);
    $phone = Validator::phone($phone);
    $name = Validator::maxLength($name, 100, 'name');
    $password = Validator::minLength($password, 6, 'password');
    
    // Optional WhatsApp (default to phone if not provided)
    $whatsapp = !empty($data['whatsapp']) ? Validator::phone($data['whatsapp']) : $phone;
    
    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Check if email already exists
    $check_query = "SELECT id FROM users WHERE email = :email";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(':email', $email);
    $check_stmt->execute();
    
    if ($check_stmt->fetch()) {
        echo ApiResponse::badRequest('Email already registered');
        exit();
    }
    
    // Hash password
    $hashed_password = Security::hashPassword($password);
    
    // Insert new user
    $insert_query = "INSERT INTO users (name, email, password, phone, whatsapp, email_verified, is_active) 
                     VALUES (:name, :email, :password, :phone, :whatsapp, FALSE, TRUE)";
    
    $insert_stmt = $conn->prepare($insert_query);
    $insert_stmt->bindParam(':name', $name);
    $insert_stmt->bindParam(':email', $email);
    $insert_stmt->bindParam(':password', $hashed_password);
    $insert_stmt->bindParam(':phone', $phone);
    $insert_stmt->bindParam(':whatsapp', $whatsapp);
    $insert_stmt->execute();
    
    $user_id = $conn->lastInsertId();
    
    // Generate session token
    $session_token = Security::generateToken(64);
    $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
    
    // Get user device info
    $device_info = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    
    // Create session
    $session_query = "INSERT INTO user_sessions (user_id, session_token, device_info, ip_address, expires_at) 
                      VALUES (:user_id, :session_token, :device_info, :ip_address, :expires_at)";
    
    $session_stmt = $conn->prepare($session_query);
    $session_stmt->bindParam(':user_id', $user_id);
    $session_stmt->bindParam(':session_token', $session_token);
    $session_stmt->bindParam(':device_info', $device_info);
    $session_stmt->bindParam(':ip_address', $ip_address);
    $session_stmt->bindParam(':expires_at', $expires_at);
    $session_stmt->execute();
    
    // Prepare response data
    $response_data = [
        'user' => [
            'id' => $user_id,
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'whatsapp' => $whatsapp,
            'email_verified' => false,
            'phone_verified' => false
        ],
        'session' => [
            'token' => $session_token,
            'expires_at' => $expires_at
        ]
    ];
    
    echo ApiResponse::success($response_data, 'Registration successful');
    
} catch (Exception $e) {
    error_log("Registration error: " . $e->getMessage());
    echo ApiResponse::badRequest($e->getMessage());
}
?>
