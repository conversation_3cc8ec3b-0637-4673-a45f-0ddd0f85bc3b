<?php
/**
 * Order Tracking API Endpoint
 * JOKIPRO.ID - Game Boosting Services
 */

require_once '../config/database.php';

try {
    // Allow GET requests only
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        echo ApiResponse::error('Only GET method allowed', 405);
        exit();
    }
    
    // Get order number from query parameter
    $order_number = isset($_GET['order_number']) ? trim($_GET['order_number']) : '';
    
    if (empty($order_number)) {
        echo ApiResponse::badRequest('Order number is required');
        exit();
    }
    
    // Sanitize input
    $order_number = Security::sanitizeInput($order_number);
    
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();
    
    // Get order details with customer, game, and service info
    $stmt = $db->prepare("
        SELECT 
            o.id,
            o.order_number,
            o.current_rank,
            o.target_rank,
            o.account_username,
            o.special_notes,
            o.base_price,
            o.additional_cost,
            o.total_price,
            o.status,
            o.payment_status,
            o.payment_method,
            o.estimated_completion,
            o.started_at,
            o.completed_at,
            o.created_at,
            c.name as customer_name,
            c.email as customer_email,
            c.phone as customer_phone,
            g.name as game_name,
            g.slug as game_slug,
            s.name as service_name,
            s.slug as service_slug,
            s.duration_days
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        JOIN games g ON o.game_id = g.id
        JOIN services s ON o.service_id = s.id
        WHERE o.order_number = ?
    ");
    
    $stmt->execute([$order_number]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo ApiResponse::notFound('Order not found');
        exit();
    }
    
    // Get order progress history
    $stmt = $db->prepare("
        SELECT 
            status,
            message,
            screenshot_url,
            created_by,
            created_at
        FROM order_progress 
        WHERE order_id = ? 
        ORDER BY created_at DESC
    ");
    
    $stmt->execute([$order['id']]);
    $progress_history = $stmt->fetchAll();
    
    // Get payment history
    $stmt = $db->prepare("
        SELECT 
            amount,
            payment_method,
            payment_reference,
            status,
            notes,
            created_at
        FROM payments 
        WHERE order_id = ? 
        ORDER BY created_at DESC
    ");
    
    $stmt->execute([$order['id']]);
    $payment_history = $stmt->fetchAll();
    
    // Calculate progress percentage
    $progress_percentage = calculateProgressPercentage($order['status']);
    
    // Format dates
    $order['created_at'] = date('d/m/Y H:i', strtotime($order['created_at']));
    $order['estimated_completion'] = $order['estimated_completion'] ? date('d/m/Y', strtotime($order['estimated_completion'])) : null;
    $order['started_at'] = $order['started_at'] ? date('d/m/Y H:i', strtotime($order['started_at'])) : null;
    $order['completed_at'] = $order['completed_at'] ? date('d/m/Y H:i', strtotime($order['completed_at'])) : null;
    
    // Format progress history dates
    foreach ($progress_history as &$progress) {
        $progress['created_at'] = date('d/m/Y H:i', strtotime($progress['created_at']));
    }
    
    // Format payment history dates
    foreach ($payment_history as &$payment) {
        $payment['created_at'] = date('d/m/Y H:i', strtotime($payment['created_at']));
        $payment['amount'] = number_format($payment['amount'], 0, ',', '.');
    }
    
    // Prepare response data
    $response_data = [
        'order_info' => [
            'order_number' => $order['order_number'],
            'status' => $order['status'],
            'payment_status' => $order['payment_status'],
            'progress_percentage' => $progress_percentage,
            'created_at' => $order['created_at'],
            'estimated_completion' => $order['estimated_completion'],
            'started_at' => $order['started_at'],
            'completed_at' => $order['completed_at']
        ],
        'customer_info' => [
            'name' => $order['customer_name'],
            'email' => $order['customer_email'],
            'phone' => $order['customer_phone']
        ],
        'service_info' => [
            'game' => $order['game_name'],
            'service' => $order['service_name'],
            'current_rank' => $order['current_rank'],
            'target_rank' => $order['target_rank'],
            'account_username' => $order['account_username'],
            'special_notes' => $order['special_notes'],
            'duration_days' => $order['duration_days']
        ],
        'pricing_info' => [
            'base_price' => number_format($order['base_price'], 0, ',', '.'),
            'additional_cost' => number_format($order['additional_cost'], 0, ',', '.'),
            'total_price' => number_format($order['total_price'], 0, ',', '.'),
            'payment_method' => $order['payment_method']
        ],
        'progress_history' => $progress_history,
        'payment_history' => $payment_history,
        'status_info' => getStatusInfo($order['status']),
        'next_steps' => getNextSteps($order['status'], $order['payment_status'])
    ];
    
    echo ApiResponse::success($response_data, 'Order details retrieved successfully');
    
} catch (Exception $e) {
    error_log("Order tracking error: " . $e->getMessage());
    echo ApiResponse::serverError('Failed to retrieve order details');
}

/**
 * Calculate progress percentage based on status
 */
function calculateProgressPercentage($status) {
    $status_percentages = [
        'pending' => 10,
        'confirmed' => 25,
        'in_progress' => 60,
        'completed' => 100,
        'cancelled' => 0,
        'refunded' => 0
    ];
    
    return isset($status_percentages[$status]) ? $status_percentages[$status] : 0;
}

/**
 * Get status information with description and color
 */
function getStatusInfo($status) {
    $status_info = [
        'pending' => [
            'label' => 'Menunggu Konfirmasi',
            'description' => 'Pesanan sedang menunggu konfirmasi pembayaran',
            'color' => 'warning',
            'icon' => 'clock'
        ],
        'confirmed' => [
            'label' => 'Dikonfirmasi',
            'description' => 'Pembayaran telah dikonfirmasi, pesanan akan segera diproses',
            'color' => 'info',
            'icon' => 'check-circle'
        ],
        'in_progress' => [
            'label' => 'Sedang Dikerjakan',
            'description' => 'Tim kami sedang mengerjakan pesanan Anda',
            'color' => 'primary',
            'icon' => 'play-circle'
        ],
        'completed' => [
            'label' => 'Selesai',
            'description' => 'Pesanan telah selesai dikerjakan',
            'color' => 'success',
            'icon' => 'check-circle'
        ],
        'cancelled' => [
            'label' => 'Dibatalkan',
            'description' => 'Pesanan telah dibatalkan',
            'color' => 'danger',
            'icon' => 'x-circle'
        ],
        'refunded' => [
            'label' => 'Refund',
            'description' => 'Pesanan telah di-refund',
            'color' => 'secondary',
            'icon' => 'arrow-left-circle'
        ]
    ];
    
    return isset($status_info[$status]) ? $status_info[$status] : $status_info['pending'];
}

/**
 * Get next steps based on current status
 */
function getNextSteps($status, $payment_status) {
    if ($status === 'pending' && $payment_status === 'unpaid') {
        return [
            'Lakukan pembayaran sesuai metode yang dipilih',
            'Kirim bukti pembayaran via WhatsApp',
            'Tunggu konfirmasi dari admin'
        ];
    }
    
    if ($status === 'confirmed' && $payment_status === 'paid') {
        return [
            'Pesanan akan segera diproses',
            'Anda akan mendapat update progress via WhatsApp',
            'Estimasi selesai sesuai yang tertera'
        ];
    }
    
    if ($status === 'in_progress') {
        return [
            'Tim sedang mengerjakan pesanan Anda',
            'Pantau progress melalui halaman ini',
            'Hubungi admin jika ada pertanyaan'
        ];
    }
    
    if ($status === 'completed') {
        return [
            'Pesanan telah selesai',
            'Silakan cek akun game Anda',
            'Berikan review jika puas dengan layanan'
        ];
    }
    
    return ['Hubungi customer service untuk informasi lebih lanjut'];
}
?>
