// Contact Page JavaScript

// DOM Elements
const faqItems = document.querySelectorAll('.faq-item');
const contactForm = document.getElementById('contactForm');
const gameSelect = document.getElementById('game');
const serviceSelect = document.getElementById('service');

// Service options for each game
const serviceOptions = {
    'mobile-legends': [
        { value: 'rank-push', text: 'Rank Push' },
        { value: 'hero-mmr', text: 'Hero MMR Boost' },
        { value: 'win-streak', text: 'Win Streak' },
        { value: 'classic-match', text: 'Classic Match' }
    ],
    'pubg-mobile': [
        { value: 'rank-push', text: 'Rank Push' },
        { value: 'kd-boost', text: 'K/D Boost' },
        { value: 'conqueror', text: 'Ace to Conqueror' },
        { value: 'achievement', text: 'Achievement' }
    ],
    'point-blank': [
        { value: 'rank-joki', text: '<PERSON><PERSON>' },
        { value: 'kd-ratio', text: 'K/D Ratio' },
        { value: 'headshot', text: 'Headshot Farming' },
        { value: 'exp-farming', text: 'EXP Farming' },
        { value: 'clan-war', text: 'Clan War' }
    ],
    'valorant': [
        { value: 'rank-boost', text: 'Rank Boost' },
        { value: 'rr-farming', text: 'RR Farming' },
        { value: 'competitive', text: 'Competitive Match' },
        { value: 'placement', text: 'Placement Match' }
    ],
    'free-fire': [
        { value: 'rank-push', text: 'Rank Push' },
        { value: 'kd-boost', text: 'K/D Boost' },
        { value: 'booyah', text: 'Booyah Achievement' },
        { value: 'event', text: 'Event Completion' }
    ],
    'genshin-impact': [
        { value: 'ar-boost', text: 'AR Boost' },
        { value: 'spiral-abyss', text: 'Spiral Abyss' },
        { value: 'quest-complete', text: 'Quest Complete' },
        { value: 'daily-commission', text: 'Daily Commission' }
    ]
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    setupFAQ();
    setupForm();
    setupGameServiceSelector();
    setupLiveChat();
});

// FAQ Accordion
function setupFAQ() {
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', function() {
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });
            
            item.classList.toggle('active');
        });
    });
}

// Form Setup
function setupForm() {
    if (contactForm) {
        contactForm.addEventListener('submit', handleFormSubmit);
    }
}

// Handle form submission
function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(contactForm);
    const data = Object.fromEntries(formData);
    
    if (!validateForm(data)) {
        return;
    }
    
    const submitBtn = contactForm.querySelector('.btn-submit');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengirim...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        const whatsappMessage = createWhatsAppMessage(data);
        window.open(`https://wa.me/6281388209195?text=${encodeURIComponent(whatsappMessage)}`, '_blank');
        
        contactForm.reset();
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        showNotification('Pesan berhasil dikirim! Anda akan diarahkan ke WhatsApp.', 'success');
    }, 2000);
}

// Validate form
function validateForm(data) {
    const required = ['name', 'email', 'phone', 'game', 'service', 'message'];
    
    for (let field of required) {
        if (!data[field] || data[field].trim() === '') {
            showNotification(`Field ${field} harus diisi!`, 'error');
            return false;
        }
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
        showNotification('Format email tidak valid!', 'error');
        return false;
    }
    
    const phoneRegex = /^(\+62|62|0)[0-9]{9,13}$/;
    if (!phoneRegex.test(data.phone.replace(/\s|-/g, ''))) {
        showNotification('Format nomor WhatsApp tidak valid!', 'error');
        return false;
    }
    
    return true;
}

// Create WhatsApp message
function createWhatsAppMessage(data) {
    return `Halo JOKIPRO.ID! 👋

Saya ingin konsultasi layanan joki:

📝 *Detail Kontak:*
• Nama: ${data.name}
• Email: ${data.email}
• WhatsApp: ${data.phone}

🎮 *Detail Pesanan:*
• Game: ${data.game.replace('-', ' ').toUpperCase()}
• Layanan: ${data.service.replace('-', ' ')}

💬 *Pesan:*
${data.message}

Mohon informasi lebih lanjut mengenai harga dan estimasi waktu pengerjaan.

Terima kasih! 🙏`;
}

// Game Service Selector
function setupGameServiceSelector() {
    if (gameSelect && serviceSelect) {
        gameSelect.addEventListener('change', function() {
            updateServiceOptions(this.value);
        });
    }
}

// Update service options
function updateServiceOptions(selectedGame) {
    serviceSelect.innerHTML = '<option value="">Pilih Layanan</option>';
    
    if (selectedGame && serviceOptions[selectedGame]) {
        serviceOptions[selectedGame].forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            serviceSelect.appendChild(optionElement);
        });
    }
}

// Live Chat Setup
function setupLiveChat() {
    window.openLiveChat = function() {
        const message = "Halo JOKIPRO.ID! Saya ingin chat langsung dengan customer service.";
        window.open(`https://wa.me/6281388209195?text=${encodeURIComponent(message)}`, '_blank');
    };
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 5000);
}

// Auto-fill form from URL parameters
function autoFillFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.get('game')) {
        gameSelect.value = urlParams.get('game');
        updateServiceOptions(urlParams.get('game'));
    }
    
    if (urlParams.get('service')) {
        setTimeout(() => {
            serviceSelect.value = urlParams.get('service');
        }, 100);
    }
}

document.addEventListener('DOMContentLoaded', autoFillFromURL);

window.ContactUtils = {
    showNotification,
    createWhatsAppMessage,
    openLiveChat: () => window.openLiveChat()
};
