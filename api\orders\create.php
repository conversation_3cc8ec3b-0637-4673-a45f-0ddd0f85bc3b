<?php
/**
 * Create Order API Endpoint
 * JOKIPRO.ID - Game Boosting Services
 */

require_once '../config/database.php';

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo ApiResponse::error('Only POST method allowed', 405);
        exit();
    }
    
    // Get JSON input
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo ApiResponse::badRequest('Invalid JSON format');
        exit();
    }
    
    // Validate required fields
    $required_fields = [
        'customer_name', 'customer_email', 'customer_phone', 
        'game', 'service', 'account_password'
    ];
    
    $validated_data = [];
    
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            echo ApiResponse::badRequest("Field '{$field}' is required");
            exit();
        }
    }
    
    // Validate and sanitize input
    $validated_data['customer_name'] = Validator::required($data['customer_name'], 'customer_name');
    $validated_data['customer_email'] = Validator::email($data['customer_email']);
    $validated_data['customer_phone'] = Validator::phone($data['customer_phone']);
    $validated_data['game'] = Validator::required($data['game'], 'game');
    $validated_data['service'] = Validator::required($data['service'], 'service');
    $validated_data['account_password'] = Validator::required($data['account_password'], 'account_password');
    
    // Optional fields
    $validated_data['current_rank'] = isset($data['current_rank']) ? Security::sanitizeInput($data['current_rank']) : null;
    $validated_data['target_rank'] = isset($data['target_rank']) ? Security::sanitizeInput($data['target_rank']) : null;
    $validated_data['account_username'] = isset($data['account_username']) ? Security::sanitizeInput($data['account_username']) : null;
    $validated_data['special_notes'] = isset($data['special_notes']) ? Security::sanitizeInput($data['special_notes']) : null;
    $validated_data['payment_method'] = isset($data['payment_method']) ? Security::sanitizeInput($data['payment_method']) : 'DANA';
    
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();
    
    // Start transaction
    $db->beginTransaction();
    
    // Check if customer exists, if not create new customer
    $stmt = $db->prepare("SELECT id FROM customers WHERE email = ?");
    $stmt->execute([$validated_data['customer_email']]);
    $customer = $stmt->fetch();
    
    if ($customer) {
        $customer_id = $customer['id'];
        
        // Update customer info
        $stmt = $db->prepare("UPDATE customers SET name = ?, phone = ?, whatsapp = ? WHERE id = ?");
        $stmt->execute([
            $validated_data['customer_name'],
            $validated_data['customer_phone'],
            $validated_data['customer_phone'],
            $customer_id
        ]);
    } else {
        // Create new customer
        $stmt = $db->prepare("INSERT INTO customers (name, email, phone, whatsapp) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $validated_data['customer_name'],
            $validated_data['customer_email'],
            $validated_data['customer_phone'],
            $validated_data['customer_phone']
        ]);
        $customer_id = $db->lastInsertId();
    }
    
    // Get game ID
    $stmt = $db->prepare("SELECT id FROM games WHERE slug = ? AND is_active = 1");
    $stmt->execute([$validated_data['game']]);
    $game = $stmt->fetch();
    
    if (!$game) {
        $db->rollBack();
        echo ApiResponse::badRequest('Invalid game selected');
        exit();
    }
    $game_id = $game['id'];
    
    // Get service ID and price
    $stmt = $db->prepare("SELECT id, base_price, duration_days FROM services WHERE slug = ? AND game_id = ? AND is_active = 1");
    $stmt->execute([$validated_data['service'], $game_id]);
    $service = $stmt->fetch();
    
    if (!$service) {
        $db->rollBack();
        echo ApiResponse::badRequest('Invalid service selected');
        exit();
    }
    $service_id = $service['id'];
    $base_price = $service['base_price'];
    $duration_days = $service['duration_days'];
    
    // Calculate total price (can add additional costs based on rank difference, etc.)
    $additional_cost = 0;
    $total_price = $base_price + $additional_cost;
    
    // Calculate estimated completion date
    $estimated_completion = date('Y-m-d', strtotime("+{$duration_days} days"));
    
    // Encrypt sensitive data
    $encrypted_password = Security::encryptSensitiveData($validated_data['account_password']);
    
    // Create order
    $stmt = $db->prepare("
        INSERT INTO orders (
            customer_id, game_id, service_id, current_rank, target_rank,
            account_username, account_password, special_notes,
            base_price, additional_cost, total_price,
            payment_method, estimated_completion
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $customer_id,
        $game_id,
        $service_id,
        $validated_data['current_rank'],
        $validated_data['target_rank'],
        $validated_data['account_username'],
        $encrypted_password,
        $validated_data['special_notes'],
        $base_price,
        $additional_cost,
        $total_price,
        $validated_data['payment_method'],
        $estimated_completion
    ]);
    
    $order_id = $db->lastInsertId();
    
    // Get the generated order number
    $stmt = $db->prepare("SELECT order_number FROM orders WHERE id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    $order_number = $order['order_number'];
    
    // Add initial progress entry
    $stmt = $db->prepare("INSERT INTO order_progress (order_id, status, message) VALUES (?, ?, ?)");
    $stmt->execute([
        $order_id,
        'pending',
        'Order created and waiting for payment confirmation'
    ]);
    
    // Commit transaction
    $db->commit();
    
    // Prepare response data
    $response_data = [
        'order_id' => $order_id,
        'order_number' => $order_number,
        'customer_name' => $validated_data['customer_name'],
        'customer_email' => $validated_data['customer_email'],
        'customer_phone' => $validated_data['customer_phone'],
        'game' => $validated_data['game'],
        'service' => $validated_data['service'],
        'total_price' => $total_price,
        'payment_method' => $validated_data['payment_method'],
        'estimated_completion' => $estimated_completion,
        'status' => 'pending',
        'whatsapp_url' => generateWhatsAppURL($validated_data, $order_number, $total_price)
    ];
    
    echo ApiResponse::success($response_data, 'Order created successfully', 201);
    
} catch (Exception $e) {
    // Rollback transaction if it was started
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
    }
    
    error_log("Order creation error: " . $e->getMessage());
    echo ApiResponse::serverError('Failed to create order: ' . $e->getMessage());
}

/**
 * Generate WhatsApp URL for order confirmation
 */
function generateWhatsAppURL($data, $order_number, $total_price) {
    $phone = "6281388209195"; // Your WhatsApp number
    
    $message = "🎮 *PESANAN BARU JOKIPRO.ID* 🎮\n\n";
    $message .= "📋 *Detail Pesanan:*\n";
    $message .= "• Order ID: {$order_number}\n";
    $message .= "• Nama: {$data['customer_name']}\n";
    $message .= "• Email: {$data['customer_email']}\n";
    $message .= "• WhatsApp: {$data['customer_phone']}\n\n";
    
    $message .= "🎯 *Layanan:*\n";
    $message .= "• Game: " . ucwords(str_replace('-', ' ', $data['game'])) . "\n";
    $message .= "• Service: " . ucwords(str_replace('-', ' ', $data['service'])) . "\n";
    
    if (!empty($data['current_rank'])) {
        $message .= "• Current Rank: {$data['current_rank']}\n";
    }
    if (!empty($data['target_rank'])) {
        $message .= "• Target Rank: {$data['target_rank']}\n";
    }
    
    $message .= "\n💰 *Total: Rp " . number_format($total_price, 0, ',', '.') . "*\n";
    $message .= "💳 *Payment: {$data['payment_method']}*\n\n";
    
    if (!empty($data['special_notes'])) {
        $message .= "📝 *Catatan:*\n{$data['special_notes']}\n\n";
    }
    
    $message .= "✅ Mohon konfirmasi pesanan ini untuk melanjutkan proses pembayaran.\n\n";
    $message .= "Terima kasih telah memilih JOKIPRO.ID! 🙏";
    
    return "https://wa.me/{$phone}?text=" . urlencode($message);
}
