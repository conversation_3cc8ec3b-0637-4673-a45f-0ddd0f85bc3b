<?php
/**
 * User Logout API Endpoint
 * JOKIPRO.ID - Game Boosting Services
 */

require_once '../config/database.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo ApiResponse::error('Method not allowed', 405);
    exit();
}

try {
    // Get session token from header or POST data
    $session_token = null;
    
    // Check Authorization header
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        $auth_header = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $session_token = $matches[1];
        }
    }
    
    // If not in header, check POST data
    if (!$session_token) {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);
        $session_token = $data['session_token'] ?? '';
    }
    
    if (empty($session_token)) {
        echo ApiResponse::badRequest('Session token required');
        exit();
    }
    
    // Connect to database
    $database = new Database();
    $conn = $database->getConnection();
    
    // Delete session
    $delete_query = "DELETE FROM user_sessions WHERE session_token = :session_token";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bindParam(':session_token', $session_token);
    $delete_stmt->execute();
    
    if ($delete_stmt->rowCount() > 0) {
        echo ApiResponse::success(null, 'Logout successful');
    } else {
        echo ApiResponse::badRequest('Invalid session token');
    }
    
} catch (Exception $e) {
    error_log("Logout error: " . $e->getMessage());
    echo ApiResponse::serverError('Logout failed');
}
?>
