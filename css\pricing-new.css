/* Pricing Page Styles */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 3rem;
}

/* Price Calculator */
.price-calculator {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.price-calculator h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #ffd700;
}

.calculator-form {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.calc-input {
    flex: 1;
    min-width: 150px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.price-result {
    flex: 1;
    min-width: 200px;
    padding: 12px 16px;
    background: #ffd700;
    color: #333;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
}

/* Game Tabs */
.pricing-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.game-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 20px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.tab-btn img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}

.tab-btn:hover,
.tab-btn.active {
    border-color: #ffd700;
    background: #ffd700;
    color: #333;
    transform: translateY(-2px);
}

/* Pricing Content */
.pricing-content {
    display: none;
}

.pricing-content.active {
    display: block;
}

.game-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    font-size: 2rem;
    font-weight: 600;
    color: #333;
}

.game-title img {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    object-fit: cover;
}

/* Pricing Grid */
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.pricing-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border-color: #ffd700;
}

.pricing-card.popular {
    border-color: #28a745;
    box-shadow: 0 6px 25px rgba(40, 167, 69, 0.2);
}

.popular-badge {
    position: absolute;
    top: -8px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.card-header {
    text-align: center;
    margin-bottom: 2rem;
}

.service-icon {
    margin-bottom: 1rem;
}

.service-icon i {
    font-size: 2.5rem;
    color: #ffd700;
}

.card-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
}

/* Price List */
.price-list {
    margin-bottom: 2rem;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.price-item:last-child {
    border-bottom: none;
}

.rank {
    font-size: 0.95rem;
    color: #666;
}

.price {
    font-size: 1rem;
    font-weight: 600;
    color: #28a745;
}

/* Features */
.features {
    margin-bottom: 2rem;
}

.features p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.btn-order-service {
    width: 100%;
    padding: 12px;
    background: #ffd700;
    color: #333;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    display: block;
}

.btn-order-service:hover {
    background: #ffed4e;
    transform: translateY(-2px);
}

/* Package Deals */
.package-deals {
    margin-top: 4rem;
    padding: 3rem 0;
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.package-deals h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 3rem;
}

.deals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.deal-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.deal-card:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
    background: white;
}

.deal-card.popular {
    border-color: #28a745;
    background: white;
}

.deal-badge {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    background: #dc3545;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.deal-card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.deal-price {
    margin-bottom: 1.5rem;
}

.original {
    text-decoration: line-through;
    color: #999;
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.discounted {
    font-size: 1.5rem;
    font-weight: 700;
    color: #28a745;
}

.deal-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
}

.deal-features li {
    color: #666;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.btn-deal {
    width: 100%;
    padding: 12px;
    background: #28a745;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: block;
}

.btn-deal:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: white;
}

.faq-section h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 3rem;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.faq-item:hover {
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.faq-item h3 {
    color: #333;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
}

/* Price Calculator */
.price-calculator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin: 60px 0;
    border-radius: 20px;
    text-align: center;
}

.price-calculator h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.price-calculator p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.calculator-form {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255,255,255,0.1);
    padding: 40px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.form-group {
    text-align: left;
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.form-group select {
    width: 100%;
    padding: 15px 20px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    background: white;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-group select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
}

.form-group select:disabled {
    background: #f0f0f0;
    color: #999;
    cursor: not-allowed;
}

.price-result {
    text-align: center;
    margin-top: 30px;
}

.price-display {
    background: rgba(255,255,255,0.2);
    padding: 20px 30px;
    border-radius: 15px;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    transition: all 0.3s ease;
}

.btn-order-calc {
    display: inline-block;
    background: #ffd700;
    color: #333;
    padding: 15px 40px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255,215,0,0.3);
}

.btn-order-calc:hover {
    background: #ffed4e;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255,215,0,0.4);
}

/* Responsive */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2rem;
    }

    .price-calculator h2 {
        font-size: 2rem;
    }

    .calculator-form {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .game-tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-btn {
        width: 200px;
        justify-content: center;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .deals-grid {
        grid-template-columns: 1fr;
    }
}
