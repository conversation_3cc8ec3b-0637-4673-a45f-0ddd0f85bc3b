-- JOKIPRO.ID Database Schema
-- Game Boosting Services Database
-- Created: 2024

-- Create database
CREATE DATABASE IF NOT EXISTS jokipro_db;
USE jokipro_db;

-- Table: users
-- Stores user authentication information
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255), -- For email login (hashed)
    phone VARCHAR(20),
    whatsapp VARCHAR(20),

    -- Social login providers
    google_id VARCHAR(100),
    facebook_id VARCHAR(100),

    -- Profile information
    avatar_url VARCHAR(255),
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,

    -- Account status
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Indexes
    INDEX idx_email (email),
    INDEX idx_google_id (google_id),
    INDEX idx_facebook_id (facebook_id),
    INDEX idx_phone (phone)
);

-- Table: customers
-- Stores customer information (linked to users)
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT, -- Link to users table (nullable for guest orders)
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    whatsapp VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_email (email),
    INDEX idx_phone (phone)
);

-- Table: games
-- Stores available games
CREATE TABLE games (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: services
-- Stores available services for each game
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    game_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    duration_days INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
    INDEX idx_game_id (game_id),
    INDEX idx_slug (slug)
);

-- Table: orders
-- Stores customer orders
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(20) NOT NULL UNIQUE,
    customer_id INT NOT NULL,
    game_id INT NOT NULL,
    service_id INT NOT NULL,
    
    -- Order details
    current_rank VARCHAR(50),
    target_rank VARCHAR(50),
    account_username VARCHAR(100),
    account_password VARCHAR(255), -- Should be encrypted
    special_notes TEXT,
    
    -- Pricing
    base_price DECIMAL(10,2) NOT NULL,
    additional_cost DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL,
    
    -- Status and timing
    status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_status ENUM('unpaid', 'partial', 'paid', 'refunded') DEFAULT 'unpaid',
    payment_method VARCHAR(50),
    
    estimated_completion DATE,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    
    INDEX idx_order_number (order_number),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Table: order_progress
-- Tracks order progress updates
CREATE TABLE order_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    status VARCHAR(50) NOT NULL,
    message TEXT,
    screenshot_url VARCHAR(255),
    created_by VARCHAR(50) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id)
);

-- Table: payments
-- Tracks payment transactions
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_reference VARCHAR(100),
    status ENUM('pending', 'success', 'failed', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_status (status)
);

-- Table: user_sessions
-- Tracks user login sessions
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    device_info TEXT,
    ip_address VARCHAR(45),
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at)
);

-- Table: password_resets
-- Tracks password reset requests
CREATE TABLE password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    reset_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_reset_token (reset_token),
    INDEX idx_expires_at (expires_at)
);

-- Insert initial games data
INSERT INTO games (name, slug, description, is_active) VALUES
('Mobile Legends', 'mobile-legends', 'MOBA game - Rank push, MMR boost, Win streak services', TRUE),
('PUBG Mobile', 'pubg-mobile', 'Battle Royale - Rank push, K/D boost, Conqueror services', TRUE),
('Genshin Impact', 'genshin-impact', 'RPG game - AR boost, Spiral Abyss, Daily commissions', TRUE),
('Point Blank', 'point-blank', 'FPS game - Rank boost, K/D improvement, Clan war services', TRUE),
('Valorant', 'valorant', 'Tactical FPS - Rank boost, RR farming, Competitive services', TRUE),
('Free Fire', 'free-fire', 'Battle Royale - Rank push, K/D boost, Badge farming', TRUE);

-- Insert initial services data for Mobile Legends
INSERT INTO services (game_id, name, slug, description, base_price, duration_days) VALUES
(1, 'Rank Push', 'rank-push', 'Push rank from current to target rank', 150000, 5),
(1, 'MMR Boost', 'mmr-boost', 'Increase hero MMR points', 100000, 3),
(1, 'Win Streak', 'win-streak', 'Achieve win streak goals', 80000, 2),
(1, 'Classic Push', 'classic-push', 'Increase classic match wins', 60000, 3);

-- Insert initial services data for PUBG Mobile
INSERT INTO services (game_id, name, slug, description, base_price, duration_days) VALUES
(2, 'Rank Push', 'rank-push', 'Push rank from Bronze to Crown/Conqueror', 200000, 7),
(2, 'K/D Boost', 'kd-boost', 'Improve Kill/Death ratio', 150000, 5),
(2, 'Conqueror Push', 'conqueror-push', 'Push to Conqueror tier', 500000, 10),
(2, 'RP Missions', 'rp-missions', 'Complete Royale Pass missions', 250000, 30),
(2, 'Win Rate Boost', 'win-rate-boost', 'Improve overall win percentage', 180000, 6);

-- Insert initial services data for Genshin Impact
INSERT INTO services (game_id, name, slug, description, base_price, duration_days) VALUES
(3, 'AR Boost', 'ar-boost', 'Adventure Rank boost 1-55', 150000, 5),
(3, 'Spiral Abyss', 'spiral-abyss', '36 stars Spiral Abyss clear', 200000, 2),
(3, 'Daily Commissions', 'daily-commissions', '30 days daily commissions', 300000, 30),
(3, 'World Quests', 'world-quests', 'Complete all world quests', 400000, 7),
(3, 'Character Build', 'character-build', 'Level 90 character + artifacts', 500000, 5),
(3, 'Material Farming', 'material-farming', 'Farm materials and resources', 250000, 3);

-- Insert initial services data for Point Blank
INSERT INTO services (game_id, name, slug, description, base_price, duration_days) VALUES
(4, 'Rank Boost', 'rank-boost', 'Rank boosting service', 120000, 4),
(4, 'K/D Improvement', 'kd-improvement', 'Improve Kill/Death ratio', 100000, 3),
(4, 'Headshot Farming', 'headshot-farming', 'Increase headshot percentage', 80000, 2),
(4, 'Daily EXP Farming', 'daily-exp-farming', 'Daily EXP and point farming', 150000, 7),
(4, 'Mission Completion', 'mission-completion', 'Complete daily/weekly missions', 90000, 3),
(4, 'Clan War Services', 'clan-war-services', 'Clan war participation and wins', 200000, 5);

-- Create views for easier data access
CREATE VIEW order_details AS
SELECT 
    o.id,
    o.order_number,
    c.name as customer_name,
    c.email as customer_email,
    c.phone as customer_phone,
    g.name as game_name,
    s.name as service_name,
    o.current_rank,
    o.target_rank,
    o.total_price,
    o.status,
    o.payment_status,
    o.created_at,
    o.estimated_completion
FROM orders o
JOIN customers c ON o.customer_id = c.id
JOIN games g ON o.game_id = g.id
JOIN services s ON o.service_id = s.id;

-- Create function to generate order number
DELIMITER //
CREATE FUNCTION generate_order_number() 
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE order_count INT;
    DECLARE order_num VARCHAR(20);
    
    SELECT COUNT(*) INTO order_count FROM orders;
    SET order_num = CONCAT('JKP', YEAR(NOW()), LPAD(order_count + 1, 6, '0'));
    
    RETURN order_num;
END //
DELIMITER ;

-- Create trigger to auto-generate order number
DELIMITER //
CREATE TRIGGER before_order_insert 
BEFORE INSERT ON orders
FOR EACH ROW
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        SET NEW.order_number = generate_order_number();
    END IF;
END //
DELIMITER ;
