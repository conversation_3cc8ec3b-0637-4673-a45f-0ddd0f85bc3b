-- Sample Data for JOKIPRO.ID Database
-- Insert sample data for testing

-- Insert sample users
INSERT INTO users (name, email, password, phone, whatsapp, email_verified, is_active) VALUES
('<PERSON>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '************', '************', TRUE, TRUE),
('<PERSON>', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '************', '************', TRUE, TRUE),
('Ahmad Rizki', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '************', '************', TRUE, TRUE),
('Google User', '<EMAIL>', NULL, '081234567893', '081234567893', TRUE, TRUE);

-- Update Google User with Google ID
UPDATE users SET google_id = 'google_123456789' WHERE email = '<EMAIL>';

-- Insert sample customers (linked to users)
INSERT INTO customers (user_id, name, email, phone, whatsapp) VALUES
(1, 'John Doe', '<EMAIL>', '************', '************'),
(2, 'Jane Smith', '<EMAIL>', '************', '************'),
(3, 'Ahmad Rizki', '<EMAIL>', '************', '************'),
(NULL, 'Guest User', '<EMAIL>', '************', '************'); -- Guest order

-- Insert sample orders
INSERT INTO orders (
    order_number, customer_id, game_id, service_id, 
    current_rank, target_rank, account_username, 
    special_notes, base_price, additional_cost, total_price,
    status, payment_status, payment_method,
    estimated_completion, started_at, created_at
) VALUES
(
    'JKP2024000001', 1, 1, 1,
    'Epic', 'Legend', 'player123',
    'Please be careful with my account',
    150000, 0, 150000,
    'in_progress', 'paid', 'dana',
    DATE_ADD(NOW(), INTERVAL 3 DAY), NOW(), NOW()
),
(
    'JKP2024000002', 2, 2, 5,
    'Gold', 'Platinum', 'pubgpro456',
    'Need fast completion',
    120000, 20000, 140000,
    'pending', 'pending', 'seabank',
    DATE_ADD(NOW(), INTERVAL 5 DAY), NULL, NOW()
),
(
    'JKP2024000003', 3, 3, 9,
    'AR 45', 'AR 55', 'genshinlover',
    'Daily commissions included',
    200000, 0, 200000,
    'completed', 'paid', 'dana',
    DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY)
);

-- Insert sample order progress
INSERT INTO order_progress (order_id, status, progress_percentage, notes, created_at) VALUES
-- Order 1 progress (in_progress)
(1, 'pending', 0, 'Order received and confirmed', DATE_SUB(NOW(), INTERVAL 2 DAY)),
(1, 'in_progress', 25, 'Started working on your account', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(1, 'in_progress', 60, 'Good progress, currently in Epic II', NOW()),

-- Order 2 progress (pending payment)
(2, 'pending', 0, 'Waiting for payment confirmation', NOW()),

-- Order 3 progress (completed)
(3, 'pending', 0, 'Order received', DATE_SUB(NOW(), INTERVAL 10 DAY)),
(3, 'in_progress', 25, 'Started AR boost', DATE_SUB(NOW(), INTERVAL 8 DAY)),
(3, 'in_progress', 50, 'Reached AR 50', DATE_SUB(NOW(), INTERVAL 5 DAY)),
(3, 'in_progress', 75, 'Almost done, AR 53', DATE_SUB(NOW(), INTERVAL 3 DAY)),
(3, 'completed', 100, 'Successfully reached AR 55!', DATE_SUB(NOW(), INTERVAL 1 DAY));

-- Insert sample payments
INSERT INTO payments (
    order_id, payment_method, amount, status,
    payment_reference, payment_date, created_at
) VALUES
(1, 'dana', 150000, 'completed', 'DANA123456789', NOW(), NOW()),
(3, 'dana', 200000, 'completed', 'DANA987654321', DATE_SUB(NOW(), INTERVAL 9 DAY), DATE_SUB(NOW(), INTERVAL 9 DAY));

-- Update order completion
UPDATE orders SET completed_at = DATE_SUB(NOW(), INTERVAL 1 DAY) WHERE id = 3;
