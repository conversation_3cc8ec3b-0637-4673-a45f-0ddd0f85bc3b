/* Back But<PERSON> */
.back-button {
    padding: 20px 0;
    background: rgba(0, 0, 0, 0.3);
}

.btn-back {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: rgba(255, 215, 0, 0.1);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 25px;
    color: #ffd700;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-left: 20px;
}

.btn-back:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
    transform: translateX(-5px);
}

/* Game Services */
.game-services {
    padding: 80px 0;
    background: #f8f9fa;
}

/* Games Grid for Services Page */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.game-tile {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.game-tile:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.game-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.game-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-tile:hover .game-image img {
    transform: scale(1.05);
}

.game-info {
    padding: 1.5rem;
    text-align: center;
}

.game-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.game-desc {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.game-price {
    color: #28a745;
    font-weight: 600;
    font-size: 1rem;
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 80px 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 100%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: #000;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.feature-card h3 {
    color: #ffd700;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    font-family: 'Orbitron', monospace;
}

.feature-card p {
    color: #ccc;
    line-height: 1.6;
}

.game-header {
    display: flex;
    align-items: center;
    gap: 2.5rem;
    margin-bottom: 3rem;
    padding: 2.5rem;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(15px);
    border-radius: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.game-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    transition: left 0.5s;
}

.game-header:hover::before {
    left: 100%;
}

.game-banner {
    width: 200px;
    height: 120px;
    object-fit: cover;
    border-radius: 15px;
    border: 3px solid rgba(255, 215, 0, 0.4);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.game-banner:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.3);
}

.game-title {
    position: relative;
    z-index: 2;
}

.game-title h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #ffd700;
    margin-bottom: 0.8rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.game-title p {
    color: #e0e0e0;
    font-size: 1.2rem;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 2px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.service-card:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.2);
    background: rgba(255, 215, 0, 0.1);
}

.service-card.featured {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

.service-badge {
    position: absolute;
    top: -10px;
    right: 15px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
    padding: 0.3rem 1rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: #000;
}

.service-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffd700;
}

.service-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 1rem;
}

.service-desc {
    color: #ccc;
    margin-bottom: 1.5rem;
    line-height: 1.5;
    font-size: 0.9rem;
}

.games-overview {
    text-align: center;
    margin-bottom: 3rem;
}

.games-overview h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 2rem;
}

.game-tile {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 215, 0, 0.2);
    text-decoration: none;
    color: inherit;
    display: block;
    cursor: pointer;
}

.game-tile:hover {
    transform: translateY(-8px);
    border-color: #ffd700;
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.3);
    background: rgba(255, 215, 0, 0.1);
}

.game-image {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.game-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-tile:hover .game-image img {
    transform: scale(1.05);
}

.game-info {
    padding: 1.5rem;
    text-align: center;
}

.game-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffd700;
}

.game-price {
    font-size: 0.9rem;
    color: #ccc;
    font-weight: 500;
}

.service-detail-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.service-detail-card:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.2);
}

.service-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-detail-card:hover .service-image img {
    transform: scale(1.05);
}

.service-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.service-content {
    padding: 2rem;
}

.service-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 1rem;
}

.service-description {
    color: #ccc;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.service-features h4 {
    color: #ffd700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.service-features ul {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    padding: 0.5rem 0;
    color: #ccc;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.service-features li i {
    color: #ffd700;
    width: 16px;
}

.service-guarantee {
    margin-bottom: 2rem;
}

.service-guarantee h4 {
    color: #ffd700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.guarantee-items {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.guarantee-item {
    background: rgba(255, 215, 0, 0.1);
    color: #ffd700;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.service-pricing {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.price-from {
    color: #ccc;
    font-size: 0.9rem;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: #ffd700;
}

/* Process Section */
.process-section {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(0, 0, 0, 0.9) 100%);
}

.process-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.process-step {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.process-step:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

.process-step h3 {
    color: #ffd700;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.process-step p {
    color: #ccc;
    line-height: 1.6;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: rgba(10, 10, 10, 0.8);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.faq-item {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

.faq-item h3 {
    color: #ffd700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.faq-item p {
    color: #ccc;
    line-height: 1.6;
}

/* Package Deals Section */
.package-deals-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.package-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.package-card.popular {
    border: 3px solid #ffd700;
    transform: scale(1.05);
}

.package-card.popular:hover {
    transform: scale(1.05) translateY(-10px);
}

.package-header {
    text-align: center;
    margin-bottom: 30px;
}

.package-badge {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.9rem;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.package-badge.starter {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.package-badge.pro {
    background: linear-gradient(135deg, #007bff, #6610f2);
    color: white;
}

.package-badge.ultimate {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
}

.popular-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: #ffd700;
    color: #333;
    padding: 5px 15px;
    border-radius: 15px;
    font-weight: 700;
    font-size: 0.8rem;
    box-shadow: 0 5px 15px rgba(255,215,0,0.3);
}

.package-card h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
}

.package-price {
    margin-bottom: 30px;
}

.original-price {
    font-size: 1.2rem;
    color: #999;
    text-decoration: line-through;
    display: block;
}

.discounted-price {
    font-size: 2.5rem;
    font-weight: 900;
    color: #28a745;
    display: block;
    margin: 10px 0;
}

.discount-percent {
    background: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.package-features {
    list-style: none;
    padding: 0;
    margin-bottom: 30px;
}

.package-features li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 15px;
}

.package-features li:last-child {
    border-bottom: none;
}

.package-features i {
    color: #28a745;
    font-size: 1.1rem;
}

.package-games {
    margin: 20px 0;
    text-align: center;
}

.package-games span {
    display: block;
    font-weight: 600;
    margin-bottom: 15px;
    color: #666;
}

.game-icons {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.game-icons img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid #ddd;
    transition: all 0.3s ease;
}

.game-icons img:hover {
    border-color: #007bff;
    transform: scale(1.1);
}

.btn-package {
    display: block;
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 10px;
    font-weight: 700;
    text-align: center;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.btn-package:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

/* Responsive for Services */
@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 2rem;
    }

    .game-banner {
        width: 100%;
        max-width: 300px;
        height: 180px;
    }

    .game-title h2 {
        font-size: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .service-content {
        padding: 1.5rem;
    }

    .guarantee-items {
        flex-direction: column;
    }

    .service-pricing {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .process-grid {
        grid-template-columns: 1fr;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .back-button {
        padding: 15px;
    }

    .btn-back {
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .service-image {
        height: 200px;
    }
    
    .service-content h3 {
        font-size: 1.5rem;
    }
    
    .price {
        font-size: 1.5rem;
    }
    
    .process-step {
        padding: 1.5rem;
    }
    
    .faq-item {
        padding: 1.5rem;
    }
}

/* Mobile Legends specific styling */
#mobile-legends-services .game-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 51, 234, 0.1));
    border: 2px solid rgba(59, 130, 246, 0.4);
}

#mobile-legends-services .game-header::before {
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
}

#mobile-legends-services .game-banner {
    border: 3px solid rgba(59, 130, 246, 0.5);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

#mobile-legends-services .game-banner:hover {
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

#mobile-legends-services .game-title h2 {
    background: linear-gradient(45deg, #3b82f6, #9333ea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

#mobile-legends-services .service-card {
    border: 1px solid rgba(59, 130, 246, 0.2);
}

#mobile-legends-services .service-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.2);
    background: rgba(59, 130, 246, 0.1);
}

#mobile-legends-services .service-card.featured {
    border: 2px solid rgba(59, 130, 246, 0.4);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.2);
    background: rgba(59, 130, 246, 0.1);
}

#mobile-legends-services .service-icon {
    background: linear-gradient(45deg, #3b82f6, #9333ea);
}

#mobile-legends-services .service-card h3 {
    color: #3b82f6;
}

#mobile-legends-services .btn-primary {
    background: linear-gradient(45deg, #3b82f6, #9333ea);
    border: none;
}

#mobile-legends-services .btn-primary:hover {
    background: linear-gradient(45deg, #2563eb, #7c3aed);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

/* Package Deals Responsive */
@media (max-width: 768px) {
    .packages-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .package-card {
        padding: 20px;
    }

    .package-card.popular {
        transform: none;
    }

    .package-card.popular:hover {
        transform: translateY(-5px);
    }

    .discounted-price {
        font-size: 2rem;
    }

    .game-icons img {
        width: 35px;
        height: 35px;
    }
}

/* Footer Info Sections */
.footer-info-section {
    padding: 60px 0;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.footer-process-section {
    padding: 60px 0;
    background: #ffffff;
    border-top: 1px solid #e9ecef;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.info-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.info-item i {
    font-size: 2.5rem;
    color: #ffd700;
    margin-bottom: 1rem;
}

.info-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.8rem;
}

.info-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.6;
    margin: 0;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.step-item {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.step-item:hover {
    border-color: #ffd700;
    background: #fff;
    transform: translateY(-3px);
}

.step-number {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #ffd700;
    color: #333;
    border-radius: 50%;
    line-height: 40px;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.step-item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.8rem;
}

.step-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}
