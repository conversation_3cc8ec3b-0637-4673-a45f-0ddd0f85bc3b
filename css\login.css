/* Login Page Styles - Professional Client-Ready Version */

.login-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 0;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    position: relative;
    overflow: hidden;
}

.login-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,215,0,0.1) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255,215,0,0.1) 25%, transparent 25%);
    background-size: 30px 30px;
    opacity: 0.3;
}

.login-container {
    max-width: 500px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 25px;
    padding: 50px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.login-header {
    text-align: center;
    margin-bottom: 50px;
}

.login-header h1 {
    font-size: 2.8rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.login-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.2rem;
    line-height: 1.5;
}

/* Social Login Buttons */
.social-login {
    margin-bottom: 30px;
}

.social-btn {
    width: 100%;
    padding: 18px 25px;
    border: none;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.social-btn:hover::before {
    left: 100%;
}

.google-btn {
    background: linear-gradient(135deg, #db4437, #c23321);
    color: white;
    box-shadow: 0 10px 25px rgba(219, 68, 55, 0.3);
}

.google-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(219, 68, 55, 0.4);
}

.facebook-btn {
    background: linear-gradient(135deg, #4267B2, #365899);
    color: white;
    box-shadow: 0 10px 25px rgba(66, 103, 178, 0.3);
}

.facebook-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(66, 103, 178, 0.4);
}

.social-btn i {
    font-size: 1.4rem;
}

/* Divider */
.divider {
    text-align: center;
    margin: 40px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
}

.divider span {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    padding: 0 25px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Login Form */
.login-form {
    margin-bottom: 40px;
}

.form-group {
    margin-bottom: 30px;
}

.form-group label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
}

.form-group input {
    width: 100%;
    padding: 18px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 15px;
    font-size: 1.1rem;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-group input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.checkbox-container input {
    margin-right: 12px;
    transform: scale(1.2);
}

.forgot-password {
    color: #ffd700;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.forgot-password:hover {
    color: #ffed4e;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.btn-login {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #0f0f23;
    border: none;
    border-radius: 15px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.btn-login:hover {
    background: linear-gradient(135deg, #ffed4e, #ffd700);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
}

/* Signup Link */
.signup-link {
    text-align: center;
    margin-bottom: 30px;
}

.signup-link p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
    margin: 0;
}

.signup-link a {
    color: #ffd700;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
}

.signup-link a:hover {
    color: #ffed4e;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Guest Option */
.guest-option {
    text-align: center;
}

.btn-guest {
    width: 100%;
    padding: 18px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-guest:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
    color: white;
}

.guest-note {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
    .login-section {
        padding: 80px 0;
    }

    .login-container {
        margin: 20px;
        padding: 40px 25px;
    }

    .login-header h1 {
        font-size: 2.2rem;
    }

    .login-header p {
        font-size: 1rem;
    }

    .social-btn {
        padding: 15px 20px;
        font-size: 1rem;
    }

    .form-group input {
        padding: 15px 18px;
    }

    .btn-login {
        padding: 15px;
        font-size: 1.1rem;
    }

    .form-options {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }
}
