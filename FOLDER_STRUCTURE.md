# JOKIPRO.ID - Folder Structure Documentation

## 📁 **CURRENT ORGANIZED STRUCTURE**

```
JokiGame/
├── 📄 index.html                    # Homepage - Landing page
├── 📄 services.html                 # Services overview page
├── 📄 pricing.html                  # Pricing page with calculator
├── 📄 contact.html                  # Contact page
├── 📄 order.html                    # Order form page
├── 📄 track-order.html              # Order tracking page
├── 📄 404.html                      # Error page
├── 📄 .htaccess                     # Apache configuration
│
├── 🎮 **GAME SERVICES PAGES**
│   ├── 📄 mobile-legends-services.html
│   ├── 📄 pubg-mobile-services.html
│   ├── 📄 genshin-services.html
│   ├── 📄 point-blank-services.html
│   ├── 📄 valorant-services.html
│   └── 📄 free-fire-services.html
│
├── 🎨 **CSS FOLDER**
│   ├── 📄 common.css               # Global styles
│   ├── 📄 home.css                 # Homepage styles
│   ├── 📄 services.css             # Services page styles
│   ├── 📄 pricing-new.css          # Pricing page styles
│   ├── 📄 contact-new.css          # Contact page styles
│   ├── 📄 order-new.css            # Order page styles
│   ├── 📄 track-order.css          # Track order styles
│   └── 🎮 **GAME-SPECIFIC CSS**
│       ├── 📄 mobile-legends.css
│       ├── 📄 pubg-mobile.css
│       ├── 📄 genshin-impact.css
│       ├── 📄 point-blank.css
│       ├── 📄 valorant.css
│       └── 📄 free-fire.css
│
├── ⚡ **JAVASCRIPT FOLDER**
│   ├── 📄 common.js                # Global functions
│   ├── 📄 mobile-nav.js            # Mobile navigation
│   ├── 📄 contact.js               # Contact form
│   ├── 📄 order.js                 # Order form logic
│   ├── 📄 pricing.js               # Pricing calculator
│   ├── 📄 services.js              # Services page
│   └── 📄 track-order.js           # Order tracking
│
├── 🖼️ **IMAGES FOLDER**
│   ├── 📄 yaemiko.jpeg             # Genshin Impact
│   ├── 📄 pubg.jpeg                # PUBG Mobile
│   ├── 📄 download (29).jpeg       # Mobile Legends
│   ├── 📄 pbbr.jpeg                # Point Blank
│   ├── 📄 Valorant.jpeg            # Valorant
│   ├── 📄 GPH Free Fire Marketing Banners - Jerome Alcantara.jpeg  # Free Fire
│   └── 📄 Hayabusa Old.jpeg        # Mobile Legends (alternative)
│
├── 🔧 **API FOLDER**
│   ├── 📁 config/
│   │   └── 📄 database.php         # Database configuration
│   └── 📁 orders/
│       ├── 📄 create.php           # Create order endpoint
│       └── 📄 track.php            # Track order endpoint
│
├── 🗄️ **DATABASE FOLDER**
│   ├── 📄 jokipro_database.sql     # Main database schema
│   └── 📄 sample_data.sql          # Sample data for testing
│
└── 📚 **DOCUMENTATION**
    ├── 📄 README.md                # Basic readme
    ├── 📄 README_FINAL.md          # Complete documentation
    └── 📄 FOLDER_STRUCTURE.md      # This file
```

## 🎯 **ORGANIZATION PRINCIPLES**

### **1. Separation of Concerns**
- **HTML**: Structure and content
- **CSS**: Styling and design
- **JS**: Functionality and interactivity
- **API**: Backend logic
- **Database**: Data storage

### **2. Naming Conventions**
- **Files**: kebab-case (mobile-legends-services.html)
- **CSS Classes**: kebab-case (.pricing-card)
- **JavaScript**: camelCase (trackOrder())
- **Database**: snake_case (order_number)

### **3. Game-Specific Organization**
Each game has:
- ✅ Dedicated service page
- ✅ Custom CSS styling
- ✅ Consistent structure
- ✅ Order integration

### **4. Asset Management**
- **Images**: Optimized for web
- **CSS**: Modular and reusable
- **JS**: Functional and documented
- **API**: RESTful endpoints

## 🚀 **BENEFITS OF THIS STRUCTURE**

### **✅ Maintainability**
- Easy to find and edit files
- Clear separation of concerns
- Consistent naming conventions

### **✅ Scalability**
- Easy to add new games
- Modular CSS and JS
- Expandable API structure

### **✅ Performance**
- Optimized file loading
- Minimal dependencies
- Clean code structure

### **✅ Developer Experience**
- Clear file organization
- Documented structure
- Easy navigation

## 📋 **FILE USAGE GUIDE**

### **Core Pages**
- `index.html` - Main landing page
- `services.html` - Game services overview
- `pricing.html` - Pricing with calculator
- `order.html` - Order form
- `track-order.html` - Order tracking

### **Game Pages**
- `*-services.html` - Individual game services
- `css/*-*.css` - Game-specific styling

### **Backend**
- `api/orders/create.php` - Process new orders
- `api/orders/track.php` - Track existing orders
- `database/*.sql` - Database setup

### **Assets**
- `css/common.css` - Global styles
- `js/common.js` - Global functions
- `images/*.jpeg` - Game images

## 🔧 **MAINTENANCE TIPS**

1. **Adding New Games**:
   - Create `new-game-services.html`
   - Add `css/new-game.css`
   - Update navigation menus
   - Add to pricing calculator

2. **Updating Styles**:
   - Global changes: `css/common.css`
   - Page-specific: `css/page-name.css`
   - Game-specific: `css/game-name.css`

3. **API Changes**:
   - Database: Update `database/*.sql`
   - Endpoints: Modify `api/orders/*.php`
   - Frontend: Update corresponding JS files

## 🎉 **READY FOR PRODUCTION**

This organized structure is:
- ✅ **Professional** - Clean and maintainable
- ✅ **Scalable** - Easy to expand
- ✅ **Optimized** - Fast loading
- ✅ **Documented** - Well explained

Perfect for hosting and long-term maintenance! 🚀
