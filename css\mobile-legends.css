/* Mobile Legends Page Styles */

/* Game Hero Section */
.game-hero {
    position: relative;
    min-height: 50vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.game-hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%);
    z-index: 2;
}

.game-hero-content {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    gap: 2rem;
    color: white;
}

.game-logo-img {
    width: 80px;
    height: 80px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Joki Services Styling */
.package-description {
    font-size: 0.9rem;
    color: #666;
    margin: 0.5rem 0;
}

.duration {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 600;
    margin: 0.5rem 0;
}

.btn-order {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.btn-order:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.package-icon i {
    font-size: 2rem;
    color: #667eea;
}
}

.game-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(45deg, #4169E1, #00BFFF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.game-subtitle {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.user-id-form {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.user-id-input, .zone-id-input {
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    width: 150px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.btn-confirm {
    padding: 12px 24px;
    background: #4169E1;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-confirm:hover {
    background: #3557C7;
    transform: translateY(-2px);
}

/* Back Button */
.back-button {
    position: absolute;
    top: 100px;
    left: 20px;
    z-index: 10;
}

.btn-back {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 10px 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-back:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateX(-5px);
}

/* Packages Section */
.packages-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.popular-tags {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.tag {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tag.popular {
    background: #28a745;
    color: white;
}

/* Packages Grid */
.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.package-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    border: 2px solid transparent;
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #4169E1;
}

.package-card.popular {
    border-color: #28a745;
    box-shadow: 0 4px 20px rgba(40, 167, 69, 0.2);
}

.popular-badge {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    background: #28a745;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.package-icon {
    margin-bottom: 1rem;
}

.diamond-icon {
    width: 40px;
    height: 40px;
    filter: hue-rotate(200deg) brightness(1.2);
}

.package-amount {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.package-price {
    margin-bottom: 0.5rem;
}

.currency {
    font-size: 0.9rem;
    color: #666;
}

.price {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.original-price {
    font-size: 0.8rem;
    color: #999;
    text-decoration: line-through;
    margin-bottom: 0.5rem;
}

.discount-badge {
    background: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* Payment Methods */
.payment-methods {
    padding: 40px 0;
    background: white;
    border-top: 1px solid #e9ecef;
}

.payment-methods h3 {
    text-align: center;
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 2rem;
}

.payment-grid {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.payment-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.payment-item:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.payment-icon {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.payment-emoji {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.payment-item span {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
    .game-hero-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .game-title {
        font-size: 2rem;
    }
    
    .user-id-form {
        flex-direction: column;
        width: 100%;
    }
    
    .user-id-input, .zone-id-input {
        width: 100%;
    }
    
    .packages-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .payment-grid {
        gap: 1rem;
    }
    
    .back-button {
        top: 80px;
        left: 10px;
    }
}

@media (max-width: 480px) {
    .packages-grid {
        grid-template-columns: 1fr;
    }
}
