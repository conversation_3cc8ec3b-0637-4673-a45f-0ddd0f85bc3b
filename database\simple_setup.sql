-- Simple Database Setup for JOKIPRO.ID
-- Compatible with older MySQL versions

-- Table: users
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255),
    phone VARCHAR(20),
    whatsapp VARCHAR(20),
    google_id VARCHAR(100),
    facebook_id VARCHAR(100),
    avatar_url VARCHAR(255),
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: user_sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    device_info TEXT,
    ip_address VARCHAR(45),
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: games
CREATE TABLE IF NOT EXISTS games (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    image_url VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: services
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    game_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    price_per_level DECIMAL(10,2) DEFAULT 0,
    estimated_hours INT DEFAULT 24,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: customers
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    whatsapp VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: orders
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(20) NOT NULL UNIQUE,
    customer_id INT NOT NULL,
    game_id INT NOT NULL,
    service_id INT NOT NULL,
    current_rank VARCHAR(50),
    target_rank VARCHAR(50),
    account_username VARCHAR(100),
    account_password VARCHAR(255),
    special_notes TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    additional_cost DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    estimated_completion TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: order_progress
CREATE TABLE IF NOT EXISTS order_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    status VARCHAR(50) NOT NULL,
    progress_percentage INT DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample games
INSERT IGNORE INTO games (name, slug, description, image_url) VALUES
('Mobile Legends', 'mobile-legends', 'MOBA game terpopuler di Indonesia', 'images/ml-bg.jpg'),
('PUBG Mobile', 'pubg-mobile', 'Battle Royale game dengan grafis terbaik', 'images/pubg-bg.jpg'),
('Genshin Impact', 'genshin-impact', 'Open world RPG dengan visual memukau', 'images/genshin-bg.jpg'),
('Point Blank', 'point-blank', 'FPS game klasik yang masih populer', 'images/pb-bg.jpg');

-- Insert sample services
INSERT IGNORE INTO services (game_id, name, description, base_price, price_per_level) VALUES
-- Mobile Legends services
(1, 'Rank Push', 'Naikkan rank dari current ke target rank', 50000, 25000),
(1, 'MMR Boost', 'Tingkatkan MMR untuk matchmaking yang lebih baik', 75000, 15000),
(1, 'Win Streak', 'Dapatkan win streak untuk rank yang stabil', 100000, 20000),
(1, 'Classic Push', 'Naikkan level classic untuk unlock ranked', 30000, 10000),

-- PUBG Mobile services  
(2, 'Rank Push', 'Push rank dari Bronze sampai Conqueror', 60000, 30000),
(2, 'K/D Boost', 'Tingkatkan Kill/Death ratio', 80000, 0),
(2, 'Achievement Hunt', 'Unlock achievement dan title', 45000, 0),
(2, 'RP Mission', 'Complete Royal Pass missions', 35000, 0),

-- Genshin Impact services
(3, 'AR Boost', 'Adventure Rank leveling service', 100000, 50000),
(3, 'Spiral Abyss', 'Clear Spiral Abyss floors', 150000, 25000),
(3, 'Daily Commissions', 'Complete daily commissions', 25000, 0),
(3, 'World Quest', 'Complete world quests and exploration', 75000, 0),

-- Point Blank services
(4, 'Rank Boost', 'Naikkan rank PB dari Trainee ke Master', 40000, 20000),
(4, 'K/D Farming', 'Improve Kill/Death ratio', 60000, 0),
(4, 'Headshot Rate', 'Increase headshot percentage', 70000, 0),
(4, 'Mission Complete', 'Complete daily and weekly missions', 30000, 0);

-- Insert sample users with hashed passwords (password: "password")
INSERT IGNORE INTO users (name, email, password, phone, whatsapp, email_verified, is_active) VALUES
('John Doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '081234567890', '081234567890', TRUE, TRUE),
('Jane Smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '081234567891', '081234567891', TRUE, TRUE),
('Ahmad Rizki', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '081234567892', '081234567892', TRUE, TRUE);

-- Insert sample customers
INSERT IGNORE INTO customers (user_id, name, email, phone, whatsapp) VALUES
(1, 'John Doe', '<EMAIL>', '081234567890', '081234567890'),
(2, 'Jane Smith', '<EMAIL>', '081234567891', '081234567891'),
(3, 'Ahmad Rizki', '<EMAIL>', '081234567892', '081234567892');
