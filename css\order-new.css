/* Order Page Styles */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 3rem;
}

/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: center;
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #ffd700;
    color: #333;
}

.step-label {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Order Section */
.order-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.order-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Order Form */
.order-form {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

.form-step h2 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-step p {
    color: #666;
    margin-bottom: 2rem;
}

/* Game Selection */
.game-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.game-option {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.game-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.game-option.selected {
    border-color: #ffd700;
    background: white;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.2);
}

.game-option img {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    object-fit: cover;
    margin-bottom: 1rem;
}

.game-option h3 {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.game-option p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.price-range {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Service Selection */
.service-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.service-option {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.service-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.service-option.selected {
    border-color: #ffd700;
    background: white;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.2);
}

.service-option h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.service-option p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.service-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: #28a745;
}

/* Form Details */
.form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-group small {
    color: #666;
    font-size: 0.8rem;
    margin-top: 0.5rem;
    display: block;
}

/* Payment Methods */
.payment-methods {
    margin-bottom: 2rem;
}

.payment-methods h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.payment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.payment-option {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.payment-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.payment-option.selected {
    border-color: #ffd700;
    background: white;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.2);
}

.payment-option img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-bottom: 0.5rem;
}

.payment-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.payment-option span {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
}

.payment-fee {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 600;
}

/* Terms Agreement */
.terms-agreement {
    margin-bottom: 2rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    color: #666;
}

.checkbox-container input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-container a {
    color: #ffd700;
    text-decoration: none;
}

.checkbox-container a:hover {
    text-decoration: underline;
}

/* Step Actions */
.step-actions {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-prev,
.btn-next,
.btn-submit {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-prev {
    background: #6c757d;
    color: white;
}

.btn-prev:hover {
    background: #5a6268;
}

.btn-next,
.btn-submit {
    background: #ffd700;
    color: #333;
}

.btn-next:hover,
.btn-submit:hover {
    background: #ffed4e;
    transform: translateY(-2px);
}

.btn-next:disabled,
.btn-submit:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

/* Order Summary */
.order-summary {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.summary-card,
.support-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.summary-card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item.total {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    border-top: 2px solid #ffd700;
    margin-top: 1rem;
    padding-top: 1rem;
}

.summary-item .label {
    color: #666;
}

.summary-item .value {
    color: #333;
    font-weight: 500;
}

.summary-divider {
    height: 1px;
    background: #e9ecef;
    margin: 1rem 0;
}

.summary-note {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.summary-note p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.summary-note p:last-child {
    margin-bottom: 0;
}

.summary-note i {
    color: #28a745;
}

/* Support Card */
.support-card h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.support-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.btn-support {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 12px;
    background: #25d366;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-support:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

/* Login Status */
.login-status {
    margin-bottom: 30px;
}

.login-prompt {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    color: white;
    margin-bottom: 20px;
}

.login-prompt-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.login-prompt-content i {
    font-size: 3rem;
    opacity: 0.8;
}

.login-prompt-content h3 {
    margin: 0 0 5px 0;
    font-size: 1.3rem;
}

.login-prompt-content p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.btn-login-prompt {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 10px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid rgba(255,255,255,0.3);
    margin-left: auto;
}

.btn-login-prompt:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.user-info {
    background: #28a745;
    border-radius: 15px;
    padding: 15px 20px;
    color: white;
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info i {
    font-size: 2rem;
}

.user-info h3 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
}

.user-info p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.btn-logout {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-left: auto;
}

.btn-logout:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    text-decoration: none;
}

/* Responsive */
@media (max-width: 768px) {
    .page-header {
        padding: 100px 0 60px;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .progress-steps {
        gap: 1rem;
        flex-wrap: wrap;
    }

    .step-label {
        font-size: 0.8rem;
    }

    .order-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .game-selection-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .service-selection {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .payment-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .step-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-prev,
    .btn-next,
    .btn-submit {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 90px 0 50px;
    }

    .page-header h1 {
        font-size: 1.8rem;
    }

    .game-selection-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
    }

    .step {
        min-width: 80px;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .step-label {
        font-size: 0.7rem;
    }

    .payment-grid {
        grid-template-columns: 1fr;
    }

    .order-form {
        padding: 1.5rem;
    }

    .summary-card,
    .support-card {
        padding: 1.5rem;
    }
}
