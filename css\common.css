/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #fff;
    background: #0a0a0a;
    overflow-x: hidden;
    padding-top: 80px; /* Space for fixed navbar */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    z-index: 9999;
    padding: 1rem 0;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
    transform: translateY(0);
}

.navbar.hidden {
    transform: translateY(-100%);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.nav-logo i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
    color: #ffd700;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #fff;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

.nav-link:hover,
.nav-link.active {
    color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: #ffd700;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

.btn-order {
    background: #ffd700 !important;
    color: #333 !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-weight: 600 !important;
}

.btn-order:hover {
    background: #ffed4e !important;
    transform: translateY(-2px) !important;
}

/* Dropdown Navigation */
.nav-dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-toggle i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.nav-dropdown:hover .dropdown-toggle i {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    gap: 1rem;
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-section {
    flex: 1;
}

.dropdown-section h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.dropdown-section a {
    display: block;
    color: #666;
    text-decoration: none;
    padding: 0.5rem 0;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.dropdown-section a:hover {
    color: #ffd700;
    padding-left: 0.5rem;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
    z-index: 1001;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--gold);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.nav-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active span:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.bar {
    width: 25px;
    height: 3px;
    background: #fff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Buttons */
.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
    background: linear-gradient(45deg, #ffed4e, #ffd700);
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #ffd700;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: #ffd700;
    color: #000;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.btn-outline {
    background: transparent;
    color: #ffd700;
    border: 2px solid #ffd700;
}

.btn-outline:hover {
    background: #ffd700;
    color: #000;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

.btn-sm {
    padding: 10px 20px;
    font-size: 0.9rem;
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.page-header {
    padding: 140px 0 80px; /* Extra padding for fixed navbar */
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(255, 215, 0, 0.1) 100%);
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-top: -80px; /* Offset body padding */
    padding-top: 200px; /* Compensate for negative margin */
}

/* Game-specific backgrounds */
body.genshin-page .page-header {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%),
        url('../images/yaemiko.jpeg') center/cover no-repeat;
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

body.genshin-page .page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.4) 100%);
    z-index: 1;
}

body.genshin-page .page-header > * {
    position: relative;
    z-index: 2;
}

body.pubg-page .page-header {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%),
        url('../images/pabji.jpeg') center/cover no-repeat;
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

body.pubg-page .page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.4) 100%);
    z-index: 1;
}

body.pubg-page .page-header > * {
    position: relative;
    z-index: 2;
}

body.ml-page .page-header {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%),
        url('../images/Hayabusa%20Old.jpeg') center/cover no-repeat;
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

body.ml-page .page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.4) 100%);
    z-index: 1;
}

body.ml-page .page-header > * {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    color: #ccc;
    max-width: 600px;
    margin: 0 auto;
}

/* Hide any unwanted elements */
.footer-links {
    display: none !important;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 100%);
    color: white;
    padding: 4rem 0 2rem;
    border-top: 2px solid rgba(255, 215, 0, 0.3);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #ffd700, transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-section {
    text-align: left;
}

.footer-section h3 {
    color: #ffd700;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-right: 0.8rem;
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: #bbb;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-section ul li a:hover {
    color: #ffd700;
    transform: translateX(5px);
}

.footer-section ul li a::before {
    content: '▶';
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.footer-section ul li a:hover::before {
    opacity: 1;
}

.footer-bottom {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    text-align: center;
}

.footer-bottom p {
    color: #888;
    font-size: 0.9rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(15px);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.3);
        padding: 2rem 0;
        border-top: 1px solid rgba(255, 215, 0, 0.2);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .section-title {
        font-size: 2rem;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-section {
        text-align: center;
    }

    .footer-logo {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .page-header h1 {
        font-size: 2rem;
    }

    .btn {
        padding: 12px 24px;
        font-size: 0.9rem;
        width: 100%;
        max-width: 280px;
    }

    /* Better mobile spacing */
    .container {
        padding: 0 1rem;
    }

    .page-header {
        padding: 3rem 0 2rem;
    }

    .page-header p {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    /* Mobile navigation improvements */
    .navbar {
        padding: 0 1rem;
        height: 60px;
    }

    .nav-container {
        padding: 0;
        height: 60px;
    }

    .nav-logo {
        font-size: 1.2rem;
    }

    .nav-logo span {
        font-size: 1.3rem;
    }

    .nav-toggle {
        display: block;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 60px;
        flex-direction: column;
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(15px);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        z-index: 1000;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu .nav-link {
        padding: 1rem;
        display: block;
        border-bottom: 1px solid rgba(255, 215, 0, 0.1);
    }

    /* Footer mobile */
    .footer-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .footer-section {
        margin-bottom: 1rem;
    }

    .footer-section h3 {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }
}
