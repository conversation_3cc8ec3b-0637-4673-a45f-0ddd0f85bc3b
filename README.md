# JOKIPRO.ID - Game Boosting Services Website

🎮 **Professional Game Boosting Services Website** - Siap untuk hosting dan production!

## 📋 **OVERVIEW**

Website jasa joki game profesional dengan fitur lengkap untuk:
- **4 Game Utama**: Mobile Legends, PUBG Mobile, Genshin Impact, Point Blank
- **Order System**: Form pemesanan terintegrasi WhatsApp
- **Order Tracking**: Sistem pelacakan pesanan real-time
- **Database Integration**: MySQL database dengan API PHP
- **Responsive Design**: Mobile-friendly dan desktop optimized

---

## 🚀 **FITUR UTAMA**

### ✅ **Website Pages**
- **Home Page** (`index.html`) - Landing page dengan game cards
- **Services** (`services.html`) - Overview semua layanan
- **Game-Specific Pages**:
  - Mobile Legends (`mobile-legends-services.html`)
  - PUBG Mobile (`pubg-mobile-services.html`) 
  - Genshin Impact (`genshin-services.html`)
  - Point Blank (`point-blank-services.html`)
  - Valorant (`valorant-services.html`)
  - Free Fire (`free-fire-services.html`)
- **Pricing** (`pricing.html`) - Daftar harga lengkap
- **Contact** (`contact.html`) - Form kontak terintegrasi
- **Order** (`order.html`) - Form pemesanan
- **Track Order** (`track-order.html`) - Pelacakan pesanan

### ✅ **Backend System**
- **Database** (`database/jokipro_database.sql`) - MySQL schema lengkap
- **API Endpoints**:
  - `api/orders/create.php` - Buat pesanan baru
  - `api/orders/track.php` - Tracking pesanan
  - `api/config/database.php` - Konfigurasi database

### ✅ **Contact Integration**
- **WhatsApp**: +62 813-8820-9195 (nomor asli user)
- **Email**: <EMAIL>
- **Auto WhatsApp Message**: Template pesan otomatis

---

## 🛠 **TEKNOLOGI**

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Styling**: Custom CSS + Font Awesome icons
- **Responsive**: Mobile-first design

---

## 📁 **STRUKTUR FILE**

```
JokiGame/
├── index.html                 # Home page
├── services.html             # Services overview
├── pricing.html              # Pricing page
├── contact.html              # Contact page
├── order.html                # Order form
├── track-order.html          # Order tracking
├── *-services.html           # Game-specific pages
├── css/                      # Stylesheets
│   ├── common.css           # Global styles
│   ├── home.css             # Home page styles
│   ├── services.css         # Services styles
│   ├── pricing-new.css      # Pricing styles
│   ├── contact.css          # Contact styles
│   ├── order-new.css        # Order form styles
│   ├── track-order.css      # Tracking styles
│   └── game-specific.css    # Game theme styles
├── js/                       # JavaScript files
│   ├── common.js            # Global functions
│   ├── contact.js           # Contact form
│   ├── order.js             # Order form
│   ├── track-order.js       # Order tracking
│   └── pricing.js           # Pricing calculator
├── api/                      # Backend API
│   ├── config/
│   │   └── database.php     # DB config & helpers
│   └── orders/
│       ├── create.php       # Create order
│       └── track.php        # Track order
├── database/
│   └── jokipro_database.sql # Database schema
└── images/                   # Game images
    ├── yaemiko.jpeg         # Genshin Impact
    ├── pubg.jpeg            # PUBG Mobile
    └── download (29).jpeg   # Mobile Legends
```

---

## 🗄️ **DATABASE SETUP**

### 1. **Import Database**
```sql
-- Import file database/jokipro_database.sql ke MySQL
mysql -u root -p < database/jokipro_database.sql
```

### 2. **Konfigurasi Database**
Edit `api/config/database.php`:
```php
private $host = "localhost";
private $db_name = "jokipro_db";
private $username = "root";        // Ganti untuk production
private $password = "";            // Ganti untuk production
```

### 3. **Tables Created**
- `customers` - Data customer
- `games` - Master data game
- `services` - Master data layanan
- `orders` - Data pesanan
- `order_progress` - Progress tracking
- `payments` - Data pembayaran

---

## 🌐 **HOSTING SETUP**

### **Requirements**
- PHP 7.4+ dengan extensions: PDO, MySQL
- MySQL 5.7+ atau MariaDB 10.2+
- Web server (Apache/Nginx)
- HTTPS support (recommended)

### **Upload Files**
1. Upload semua file ke public_html/
2. Import database schema
3. Update database config
4. Set file permissions (755 untuk folders, 644 untuk files)

### **Recommended Hosting**
- **Free Options**: 000webhost, InfinityFree, Netlify (static)
- **Paid Options**: Hostinger, Niagahoster, DomainRacer
- **Requirements**: PHP + MySQL support, HTTPS

---

## 📱 **CONTACT INFO**

- **WhatsApp**: +62 813-8820-9195
- **Email**: <EMAIL>
- **Website**: JOKIPRO.ID

---

## 🎯 **SERVICES OFFERED**

### **Mobile Legends**
- Rank Push (Warrior → Mythic)
- Hero MMR Boost
- Win Streak Services
- Classic Match Boost

### **PUBG Mobile**
- Rank Push (Bronze → Conqueror)
- K/D Ratio Boost
- Achievement Completion
- RP Mission Services

### **Genshin Impact**
- Adventure Rank Boost
- Spiral Abyss 36★
- Daily Commissions
- World Quest Completion
- Character Building
- Material Farming

### **Point Blank**
- Rank Boosting
- K/D Ratio Improvement
- Headshot Farming
- Daily EXP Farming
- Mission Completion
- Clan War Services

---

## ✅ **TESTING CHECKLIST**

- [x] All pages load correctly
- [x] Navigation links work
- [x] Contact forms functional
- [x] WhatsApp integration working
- [x] Order system operational
- [x] Track order system ready
- [x] Database schema complete
- [x] API endpoints functional
- [x] Mobile responsive design
- [x] Cross-browser compatibility

---

## 🚀 **READY FOR PRODUCTION**

Website ini **100% siap untuk hosting** dengan:
- ✅ Semua nomor WhatsApp sudah diganti ke nomor asli
- ✅ Content top-up sudah dihapus, fokus joki services
- ✅ Database dan API sudah lengkap
- ✅ Order tracking system sudah jadi
- ✅ Design responsive dan professional
- ✅ SEO-friendly structure

**Tinggal upload ke hosting dan website siap digunakan!** 🎉

---

*Developed by AI Assistant for professional game boosting services*
