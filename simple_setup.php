<?php
/**
 * Simple Database Setup Script
 * JOKIPRO.ID - Game Boosting Services
 */

// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "jokipro_db";

?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JOKIPRO.ID Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #ffd700; padding-bottom: 10px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .step { margin: 15px 0; padding: 10px; border-left: 4px solid #ffd700; background: #f9f9f9; }
        .test-accounts { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .links { margin: 20px 0; }
        .links a { display: inline-block; margin: 5px 10px 5px 0; padding: 10px 20px; background: #ffd700; color: #333; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .links a:hover { background: #ffed4e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 JOKIPRO.ID Database Setup</h1>
        
        <?php
        try {
            echo "<div class='step'><strong>Step 1:</strong> Connecting to MySQL server...</div>";
            
            // Connect to MySQL server (without database)
            $pdo = new PDO("mysql:host=$host", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "<p class='success'>✓ Connected to MySQL server</p>";
            
            echo "<div class='step'><strong>Step 2:</strong> Creating database...</div>";
            
            // Create database if not exists
            $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
            echo "<p class='success'>✓ Database '$database' created/verified</p>";
            
            // Use the database
            $pdo->exec("USE $database");
            echo "<p class='success'>✓ Using database '$database'</p>";
            
            echo "<div class='step'><strong>Step 3:</strong> Creating tables and inserting data...</div>";
            
            // Read and execute the simple setup SQL
            $setup_file = 'database/simple_setup.sql';
            if (file_exists($setup_file)) {
                $setup_sql = file_get_contents($setup_file);
                
                // Split by semicolon and execute each statement
                $statements = array_filter(array_map('trim', explode(';', $setup_sql)));
                
                $success_count = 0;
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^--/', $statement)) {
                        try {
                            $pdo->exec($statement);
                            $success_count++;
                        } catch (PDOException $e) {
                            // Only show errors that are not about existing data
                            if (strpos($e->getMessage(), 'already exists') === false && 
                                strpos($e->getMessage(), 'Duplicate entry') === false) {
                                echo "<p class='warning'>⚠ " . $e->getMessage() . "</p>";
                            }
                        }
                    }
                }
                echo "<p class='success'>✓ Executed $success_count SQL statements</p>";
            } else {
                echo "<p class='error'>❌ Setup file not found: $setup_file</p>";
                exit();
            }
            
            echo "<div class='step'><strong>Step 4:</strong> Verifying setup...</div>";
            
            // Check tables
            $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            echo "<p class='success'>✓ Tables created: " . implode(', ', $tables) . "</p>";
            
            // Check data
            $user_count = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
            $game_count = $pdo->query("SELECT COUNT(*) FROM games")->fetchColumn();
            $service_count = $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn();
            
            echo "<p class='success'>✓ Users: $user_count records</p>";
            echo "<p class='success'>✓ Games: $game_count records</p>";
            echo "<p class='success'>✓ Services: $service_count records</p>";
            
            echo "<h2 class='success'>🎉 Database Setup Complete!</h2>";
            
            echo "<div class='test-accounts'>";
            echo "<h3>🔑 Test Accounts:</h3>";
            echo "<ul>";
            echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> password</li>";
            echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> password</li>";
            echo "<li><strong>Email:</strong> <EMAIL> | <strong>Password:</strong> password</li>";
            echo "</ul>";
            echo "</div>";
            
            echo "<div class='links'>";
            echo "<h3>🚀 Test Your Website:</h3>";
            echo "<a href='index.html'>🏠 Home Page</a>";
            echo "<a href='login.html'>🔐 Login Page</a>";
            echo "<a href='register.html'>📝 Register Page</a>";
            echo "<a href='pricing.html'>💰 Pricing Page</a>";
            echo "<a href='track-order.html'>📦 Track Order</a>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<h2 class='error'>❌ Database Setup Failed</h2>";
            echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
            echo "<div class='info'>";
            echo "<h3>💡 Troubleshooting:</h3>";
            echo "<ul>";
            echo "<li>Make sure XAMPP/WAMP is running</li>";
            echo "<li>Check if MySQL service is started</li>";
            echo "<li>Verify database credentials in the script</li>";
            echo "<li>Try refreshing this page</li>";
            echo "</ul>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
