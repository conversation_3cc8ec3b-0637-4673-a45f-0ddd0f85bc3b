/**
 * Track Order JavaScript
 * JOKIPRO.ID - Game Boosting Services
 */

// DOM Elements
const orderNumberInput = document.getElementById('orderNumber');
const orderSearchSection = document.getElementById('orderSearch');
const orderDetailsSection = document.getElementById('orderDetails');
const loadingOverlay = document.getElementById('loadingOverlay');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Check if order number is in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const orderNumber = urlParams.get('order');
    
    if (orderNumber) {
        orderNumberInput.value = orderNumber;
        trackOrder();
    }
    
    // Add enter key listener to input
    orderNumberInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            trackOrder();
        }
    });
});

/**
 * Track order function
 */
async function trackOrder() {
    const orderNumber = orderNumberInput.value.trim();
    
    if (!orderNumber) {
        showNotification('Masukkan order number terlebih dahulu', 'error');
        return;
    }
    
    // Validate order number format
    if (!orderNumber.match(/^JKP\d{10}$/)) {
        showNotification('Format order number tidak valid. Contoh: JKP2024000001', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        // Demo mode - if order number is DEMO, show demo data
        if (orderNumber.toUpperCase() === 'DEMO' || orderNumber === 'JKP2024000001') {
            const demoData = getDemoOrderData();
            displayOrderDetails(demoData);
            orderSearchSection.style.display = 'none';
            orderDetailsSection.style.display = 'block';
            orderDetailsSection.scrollIntoView({ behavior: 'smooth' });
            showLoading(false);
            return;
        }

        const response = await fetch(`api/orders/track.php?order_number=${encodeURIComponent(orderNumber)}`);
        const result = await response.json();

        if (result.success) {
            displayOrderDetails(result.data);
            orderSearchSection.style.display = 'none';
            orderDetailsSection.style.display = 'block';

            // Scroll to order details
            orderDetailsSection.scrollIntoView({ behavior: 'smooth' });
        } else {
            showNotification(result.message || 'Order tidak ditemukan', 'error');
        }
    } catch (error) {
        console.error('Error tracking order:', error);
        showNotification('Terjadi kesalahan saat mencari order', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Display order details
 */
function displayOrderDetails(data) {
    const { order_info, customer_info, service_info, pricing_info, progress_history, status_info, next_steps } = data;
    
    // Update order number and status
    document.getElementById('orderNumberDisplay').textContent = order_info.order_number;
    
    const statusBadge = document.getElementById('statusBadge');
    statusBadge.textContent = status_info.label;
    statusBadge.className = `status-badge ${order_info.status}`;
    
    document.getElementById('statusDescription').textContent = status_info.description;
    
    // Update progress circle
    updateProgressCircle(order_info.progress_percentage);
    
    // Populate service info
    const serviceInfoHtml = `
        <div class="info-item">
            <span class="info-label">Game</span>
            <span class="info-value">${service_info.game}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Service</span>
            <span class="info-value">${service_info.service}</span>
        </div>
        ${service_info.current_rank ? `
        <div class="info-item">
            <span class="info-label">Current Rank</span>
            <span class="info-value">${service_info.current_rank}</span>
        </div>` : ''}
        ${service_info.target_rank ? `
        <div class="info-item">
            <span class="info-label">Target Rank</span>
            <span class="info-value">${service_info.target_rank}</span>
        </div>` : ''}
        ${service_info.account_username ? `
        <div class="info-item">
            <span class="info-label">Account</span>
            <span class="info-value">${service_info.account_username}</span>
        </div>` : ''}
        <div class="info-item">
            <span class="info-label">Duration</span>
            <span class="info-value">${service_info.duration_days} hari</span>
        </div>
        <div class="info-item">
            <span class="info-label">Order Date</span>
            <span class="info-value">${order_info.created_at}</span>
        </div>
        ${order_info.estimated_completion ? `
        <div class="info-item">
            <span class="info-label">Estimasi Selesai</span>
            <span class="info-value">${order_info.estimated_completion}</span>
        </div>` : ''}
    `;
    document.getElementById('serviceInfo').innerHTML = serviceInfoHtml;
    
    // Populate customer info
    const customerInfoHtml = `
        <div class="info-item">
            <span class="info-label">Nama</span>
            <span class="info-value">${customer_info.name}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Email</span>
            <span class="info-value">${customer_info.email}</span>
        </div>
        <div class="info-item">
            <span class="info-label">WhatsApp</span>
            <span class="info-value">${customer_info.phone}</span>
        </div>
    `;
    document.getElementById('customerInfo').innerHTML = customerInfoHtml;
    
    // Populate pricing info
    const pricingInfoHtml = `
        <div class="info-item">
            <span class="info-label">Base Price</span>
            <span class="info-value">Rp ${pricing_info.base_price}</span>
        </div>
        ${pricing_info.additional_cost !== 'Rp 0' ? `
        <div class="info-item">
            <span class="info-label">Additional Cost</span>
            <span class="info-value">Rp ${pricing_info.additional_cost}</span>
        </div>` : ''}
        <div class="info-item">
            <span class="info-label"><strong>Total Price</strong></span>
            <span class="info-value"><strong>Rp ${pricing_info.total_price}</strong></span>
        </div>
        <div class="info-item">
            <span class="info-label">Payment Method</span>
            <span class="info-value">${pricing_info.payment_method}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Payment Status</span>
            <span class="info-value">${getPaymentStatusLabel(order_info.payment_status)}</span>
        </div>
    `;
    document.getElementById('pricingInfo').innerHTML = pricingInfoHtml;
    
    // Populate progress timeline
    const timelineHtml = progress_history.map(item => `
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-status">${getStatusLabel(item.status)}</div>
                <div class="timeline-message">${item.message}</div>
                <div class="timeline-date">${item.created_at}</div>
            </div>
        </div>
    `).join('');
    document.getElementById('progressTimeline').innerHTML = timelineHtml;
    
    // Populate next steps
    const stepsHtml = next_steps.map((step, index) => `
        <div class="step-item">
            <div class="step-number">${index + 1}</div>
            <div class="step-text">${step}</div>
        </div>
    `).join('');
    document.getElementById('nextSteps').innerHTML = stepsHtml;
}

/**
 * Update progress circle
 */
function updateProgressCircle(percentage) {
    const circle = document.getElementById('progressCircle');
    const text = document.getElementById('progressText');
    
    const circumference = 2 * Math.PI * 35; // radius = 35
    const offset = circumference - (percentage / 100) * circumference;
    
    circle.style.strokeDashoffset = offset;
    text.textContent = `${percentage}%`;
    
    // Animate the progress
    circle.style.transition = 'stroke-dashoffset 1s ease-in-out';
}

/**
 * Get payment status label
 */
function getPaymentStatusLabel(status) {
    const labels = {
        'unpaid': 'Belum Dibayar',
        'partial': 'Dibayar Sebagian',
        'paid': 'Lunas',
        'refunded': 'Refund'
    };
    return labels[status] || status;
}

/**
 * Get status label
 */
function getStatusLabel(status) {
    const labels = {
        'pending': 'Menunggu Konfirmasi',
        'confirmed': 'Dikonfirmasi',
        'in_progress': 'Sedang Dikerjakan',
        'completed': 'Selesai',
        'cancelled': 'Dibatalkan',
        'refunded': 'Refund'
    };
    return labels[status] || status;
}

/**
 * Show/hide loading overlay
 */
function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

/**
 * Get notification icon
 */
function getNotificationIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[type] || icons.info;
}

/**
 * Get notification color
 */
function getNotificationColor(type) {
    const colors = {
        'success': '#4CAF50',
        'error': '#f44336',
        'warning': '#ff9800',
        'info': '#2196F3'
    };
    return colors[type] || colors.info;
}

/**
 * Search again function
 */
function searchAgain() {
    orderDetailsSection.style.display = 'none';
    orderSearchSection.style.display = 'block';
    orderNumberInput.value = '';
    orderNumberInput.focus();
}

// Add search again button functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add search again button to order details section
    const searchAgainBtn = document.createElement('button');
    searchAgainBtn.textContent = 'Cari Order Lain';
    searchAgainBtn.className = 'btn-secondary';
    searchAgainBtn.onclick = searchAgain;
    searchAgainBtn.style.cssText = `
        margin-top: 20px;
        padding: 10px 20px;
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.9rem;
    `;
    
    // Add to order details section
    const container = orderDetailsSection.querySelector('.container');
    if (container) {
        container.appendChild(searchAgainBtn);
    }
});

/**
 * Get demo order data for testing
 */
function getDemoOrderData() {
    return {
        order_info: {
            order_number: 'JKP2024000001',
            status: 'in_progress',
            progress_percentage: 65,
            estimated_completion: '2024-12-28',
            started_at: '2024-12-25',
            created_at: '2024-12-24'
        },
        customer_info: {
            name: 'Demo User',
            email: '<EMAIL>',
            phone: '************'
        },
        service_info: {
            game: 'Mobile Legends',
            service: 'Rank Push',
            current_rank: 'Epic III',
            target_rank: 'Legend V',
            account_username: 'DemoPlayer123'
        },
        pricing_info: {
            base_price: 150000,
            additional_cost: 0,
            total_price: 150000,
            payment_status: 'paid',
            payment_method: 'DANA'
        },
        progress_history: [
            {
                status: 'pending',
                progress_percentage: 0,
                notes: 'Order received and payment confirmed',
                created_at: '2024-12-24 10:00:00'
            },
            {
                status: 'in_progress',
                progress_percentage: 25,
                notes: 'Started working on your account. Currently in Epic IV.',
                created_at: '2024-12-25 09:00:00'
            },
            {
                status: 'in_progress',
                progress_percentage: 65,
                notes: 'Great progress! Reached Epic II. Almost to Epic I.',
                created_at: '2024-12-26 15:30:00'
            }
        ],
        status_info: {
            label: 'In Progress',
            description: 'Our professional player is currently working on your account. Great progress so far!'
        },
        next_steps: [
            'Continue rank push to Epic I',
            'Push to Legend V (target rank)',
            'Complete order and notify customer'
        ]
    };
}
