/* Track Order Page Styles - Professional Client-Ready Version */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,215,0,0.1) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255,215,0,0.1) 25%, transparent 25%);
    background-size: 20px 20px;
    opacity: 0.3;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.page-header p {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Header Stats */
.header-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Order Search Section */
.order-search {
    padding: 80px 0;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    position: relative;
}

.order-search::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 20%, rgba(255,215,0,0.1) 2px, transparent 2px);
    background-size: 20px 20px;
}

.search-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 20px;
    padding: 50px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 700px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
}

.search-header {
    margin-bottom: 40px;
}

.search-header i {
    font-size: 4rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
}

.search-header h2 {
    font-size: 2.2rem;
    color: white;
    margin: 0;
    font-weight: 600;
}

.search-form .form-group {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.order-input {
    flex: 1;
    padding: 18px 25px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 15px;
    font-size: 1.2rem;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.order-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.order-input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.btn-track {
    padding: 18px 35px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #0f0f23;
    border: none;
    border-radius: 15px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-track:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
    background: linear-gradient(135deg, #ffed4e, #ffd700);
}

.search-help {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

.search-help i {
    color: #ffd700;
    margin-right: 8px;
}

/* Order Details Section */
.order-details {
    padding: 80px 0;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    position: relative;
}

.order-details::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(30deg, rgba(255,215,0,0.05) 12%, transparent 12.5%, transparent 87%, rgba(255,215,0,0.05) 87.5%, rgba(255,215,0,0.05)),
                linear-gradient(150deg, rgba(255,215,0,0.05) 12%, transparent 12.5%, transparent 87%, rgba(255,215,0,0.05) 87.5%, rgba(255,215,0,0.05));
    background-size: 20px 20px;
    opacity: 0.3;
}

/* Status Card */
.status-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.status-info h2 {
    font-size: 2.2rem;
    color: white;
    margin: 0 0 15px 0;
    font-weight: 700;
}

.status-badge {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.status-badge.pending { background: linear-gradient(135deg, #ff9800, #f57c00); color: white; }
.status-badge.confirmed { background: linear-gradient(135deg, #2196f3, #1976d2); color: white; }
.status-badge.in_progress { background: linear-gradient(135deg, #9c27b0, #7b1fa2); color: white; }
.status-badge.completed { background: linear-gradient(135deg, #4caf50, #388e3c); color: white; }
.status-badge.cancelled { background: linear-gradient(135deg, #f44336, #d32f2f); color: white; }

/* Progress Circle */
.progress-circle {
    position: relative;
}

.progress-ring {
    position: relative;
    width: 100px;
    height: 100px;
}

.progress-svg {
    transform: rotate(-90deg);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.4rem;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.status-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.2rem;
    line-height: 1.6;
}

/* Order Info Grid */
.order-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.info-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.card-header {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #0f0f23;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-header i {
    font-size: 1.5rem;
    font-weight: 700;
}

.card-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
}

.card-content {
    padding: 30px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    font-size: 1.1rem;
}

.info-value {
    color: white;
    font-weight: 700;
    text-align: right;
    font-size: 1.1rem;
}

/* Timeline Card */
.timeline-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.timeline {
    position: relative;
    padding-left: 40px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #ffd700, #ffed4e);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 40px;
    padding-left: 40px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border: 4px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

.timeline-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px 25px;
    border-radius: 15px;
    border-left: 4px solid #ffd700;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.timeline-status {
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.timeline-message {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 12px;
    line-height: 1.5;
}

.timeline-date {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.6);
}

/* Next Steps Card */
.next-steps-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.steps-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.step-item:last-child {
    border-bottom: none;
}

.step-number {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #0f0f23;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    font-weight: 700;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.step-text {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-size: 1.1rem;
}

/* Support Card */
.support-card {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(37, 211, 102, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.support-card h4 {
    margin: 0 0 15px 0;
    font-size: 1.6rem;
    font-weight: 700;
}

.support-card p {
    margin: 0 0 25px 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.btn-support {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 15px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-support:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 4rem;
    margin-bottom: 25px;
    color: #ffd700;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    font-size: 1.3rem;
    margin: 0;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2.5rem;
    }

    .page-header p {
        font-size: 1.1rem;
    }

    .header-stats {
        gap: 30px;
        margin-top: 30px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    .search-card {
        padding: 30px 20px;
        margin: 0 20px;
    }

    .search-form .form-group {
        flex-direction: column;
        gap: 15px;
    }

    .status-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .order-info-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .info-card, .status-card, .timeline-card, .next-steps-card {
        margin: 0 20px 30px 20px;
    }

    .timeline {
        padding-left: 25px;
    }

    .timeline-item {
        padding-left: 25px;
    }

    .timeline-item::before {
        left: -25px;
    }
}
