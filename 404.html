<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | JOKIPRO.ID</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }

        .error-code {
            font-family: 'Orbitron', monospace;
            font-size: 8rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 0 0 20px rgba(255,255,255,0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(255,255,255,0.3); }
            to { text-shadow: 0 0 30px rgba(255,255,255,0.6), 0 0 40px rgba(255,255,255,0.4); }
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: rgba(255,215,0,0.3);
            border-color: rgba(255,215,0,0.5);
        }

        .btn-primary:hover {
            background: rgba(255,215,0,0.5);
        }

        .game-icons {
            margin: 3rem 0 2rem;
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .game-icon {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            animation: float 3s ease-in-out infinite;
            backdrop-filter: blur(10px);
        }

        .game-icon:nth-child(1) { animation-delay: 0s; }
        .game-icon:nth-child(2) { animation-delay: 0.5s; }
        .game-icon:nth-child(3) { animation-delay: 1s; }
        .game-icon:nth-child(4) { animation-delay: 1.5s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .footer-text {
            margin-top: 3rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
            
            .game-icons {
                gap: 1rem;
            }
            
            .game-icon {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-title">Halaman Tidak Ditemukan</h1>
        <p class="error-message">
            Oops! Halaman yang Anda cari tidak dapat ditemukan. 
            Mungkin halaman telah dipindahkan atau URL salah.
        </p>
        
        <div class="game-icons">
            <div class="game-icon">🎮</div>
            <div class="game-icon">🏆</div>
            <div class="game-icon">⚔️</div>
            <div class="game-icon">🎯</div>
        </div>
        
        <div class="error-actions">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-home"></i>
                Kembali ke Home
            </a>
            <a href="/services" class="btn">
                <i class="fas fa-gamepad"></i>
                Lihat Services
            </a>
            <a href="/contact" class="btn">
                <i class="fas fa-headset"></i>
                Hubungi Kami
            </a>
        </div>
        
        <div class="footer-text">
            <p><strong>JOKIPRO.ID</strong> - Professional Game Boosting Services</p>
            <p>Butuh bantuan? WhatsApp: +62 813-8820-9195</p>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate error code on load
            const errorCode = document.querySelector('.error-code');
            errorCode.style.transform = 'scale(0)';
            errorCode.style.transition = 'transform 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            
            setTimeout(() => {
                errorCode.style.transform = 'scale(1)';
            }, 200);
            
            // Add click effect to game icons
            const gameIcons = document.querySelectorAll('.game-icon');
            gameIcons.forEach(icon => {
                icon.addEventListener('click', function() {
                    this.style.transform = 'scale(1.2) rotate(360deg)';
                    this.style.transition = 'transform 0.5s ease';
                    
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 500);
                });
            });
        });
    </script>
</body>
</html>
