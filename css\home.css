/* Hero Section */
.hero {
    min-height: 60vh;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)),
                url('../images/𖦹 ִ ۫ ⭒ furina.jpeg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    color: white;
    position: relative;
    overflow: hidden;
    padding: 80px 0 40px;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.1) 0%, rgba(0, 0, 0, 0.8) 70%);
    z-index: 1;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-logo {
    margin-bottom: 2rem;
}

.logo-text {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.4);
    margin-bottom: 0.5rem;
    letter-spacing: 2px;
}

.logo-dot {
    color: #ff6b35;
    text-shadow: 0 0 20px rgba(255, 107, 53, 0.8);
}

.logo-subtitle {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    color: #ccc;
    letter-spacing: 4px;
    margin-bottom: 2rem;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    color: #fff;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

/* Game Selection Section */
.game-selection {
    padding: 60px 0;
    background: #f8f9fa;
}

.section-header {
    margin: 3rem 0 2rem 0;
    text-align: left;
}

.section-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.game-tile {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    display: block;
    cursor: pointer;
    position: relative;
}

.game-tile.featured {
    border: 2px solid #28a745;
    box-shadow: 0 6px 25px rgba(40, 167, 69, 0.2);
}

.popular-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
}

.game-tile:hover {
    transform: translateY(-8px);
    border-color: #ffd700;
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.3);
    background: rgba(255, 215, 0, 0.1);
}

.game-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.game-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-tile:hover .game-image img {
    transform: scale(1.05);
}

.game-info {
    padding: 1.5rem;
    text-align: center;
}

.game-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.game-desc {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.game-price {
    color: #28a745;
    font-weight: 600;
    font-size: 1rem;
}

/* Service Categories */
.service-categories {
    padding: 80px 0;
    background: white;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.category-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #ffd700;
    background: white;
}

.category-icon {
    margin-bottom: 1.5rem;
}

.category-icon i {
    font-size: 3rem;
    color: #ffd700;
}

.category-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.category-card p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.category-games {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.category-games span {
    background: #ffd700;
    color: #333;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.game-price {
    font-size: 1rem;
    color: #ccc;
    font-weight: 500;
}

/* Why Choose Us Section */
.why-choose-footer {
    padding: 60px 0;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.feature-item .feature-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-size: 1.2rem;
}

.feature-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Footer Info Sections */
.footer-info-section {
    padding: 60px 0;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.footer-process-section {
    padding: 60px 0;
    background: #ffffff;
    border-top: 1px solid #e9ecef;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.info-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.info-item i {
    font-size: 2.5rem;
    color: #ffd700;
    margin-bottom: 1rem;
}

.info-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.8rem;
}

.info-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.6;
    margin: 0;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.step-item {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.step-item:hover {
    border-color: #ffd700;
    background: #fff;
    transform: translateY(-3px);
}

.step-number {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #ffd700;
    color: #333;
    border-radius: 50%;
    line-height: 40px;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.step-item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.8rem;
}

.step-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: #000;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffd700;
}

.feature-card p {
    color: #ccc;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(255, 215, 0, 0.1) 100%);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #ccc;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive for Home */
@media (max-width: 768px) {
    .logo-text {
        font-size: 2.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .games-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .logo-text {
        font-size: 2rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .games-grid {
        gap: 1rem;
        grid-template-columns: 1fr;
        padding: 0 15px;
    }

    .game-card {
        margin: 0 10px;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .feature-item .feature-icon {
        margin: 0 auto 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .games-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-title {
        font-size: 2rem;
    }

    .logo-text {
        font-size: 3rem;
    }
}
