// Services page game selection handler
document.addEventListener('DOMContentLoaded', function() {
    // Check URL parameters for game selection
    const urlParams = new URLSearchParams(window.location.search);
    const gameParam = urlParams.get('game');
    
    // Game mapping
    const gameMapping = {
        'genshin': 'genshin-services',
        'mobile-legends': 'mobile-legends-services',
        'pubg-mobile': 'pubg-mobile-services'
    };
    
    // Game titles
    const gameTitles = {
        'genshin': {
            title: 'Genshin Impact Services',
            subtitle: 'Adventure Rank, Spiral Abyss, Quest, dan Farming Services'
        },
        'mobile-legends': {
            title: 'Mobile Legends Services', 
            subtitle: 'Rank Push, Hero MMR, Win Streak, dan Event Services'
        },
        'pubg-mobile': {
            title: 'PUBG Mobile Services',
            subtitle: 'Rank Push, K/D Boost, Achievement, dan Event Services'
        }
    };
    
    // Show specific game services if parameter exists
    if (gameParam && gameMapping[gameParam]) {
        showGameServices(gameParam);
    } else {
        showAllGames();
    }
    
    function showGameServices(game) {
        // Hide all sections
        document.querySelectorAll('.game-section').forEach(section => {
            section.style.display = 'none';
        });
        
        // Show selected game section
        const gameSection = document.getElementById(gameMapping[game]);
        if (gameSection) {
            gameSection.style.display = 'block';
        }
        
        // Update page title
        const pageTitle = document.getElementById('page-title');
        const pageSubtitle = document.getElementById('page-subtitle');
        
        if (pageTitle && gameTitles[game]) {
            pageTitle.textContent = gameTitles[game].title;
        }
        if (pageSubtitle && gameTitles[game]) {
            pageSubtitle.textContent = gameTitles[game].subtitle;
        }
        
        // Add back button
        addBackButton();
    }
    
    function showAllGames() {
        // Hide specific game sections
        document.querySelectorAll('.game-section').forEach(section => {
            if (section.id !== 'all-games-services') {
                section.style.display = 'none';
            }
        });
        
        // Show all games overview
        const allGamesSection = document.getElementById('all-games-services');
        if (allGamesSection) {
            allGamesSection.style.display = 'block';
        }
    }
    
    function addBackButton() {
        const pageHeader = document.querySelector('.page-header .container');
        
        // Check if back button already exists
        if (document.querySelector('.back-button')) {
            return;
        }
        
        const backButton = document.createElement('a');
        backButton.href = 'services.html';
        backButton.className = 'back-button';
        backButton.innerHTML = '<i class="fas fa-arrow-left"></i> Kembali ke Semua Game';
        
        // Insert back button before title
        const title = document.getElementById('page-title');
        if (title) {
            pageHeader.insertBefore(backButton, title);
        }
    }
});

// Add CSS for back button
const backButtonStyles = document.createElement('style');
backButtonStyles.textContent = `
    .back-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: #ffd700;
        text-decoration: none;
        font-weight: 500;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        border: 1px solid rgba(255, 215, 0, 0.3);
        background: rgba(255, 215, 0, 0.1);
    }
    
    .back-button:hover {
        background: rgba(255, 215, 0, 0.2);
        transform: translateX(-3px);
        color: #ffd700;
        text-decoration: none;
    }
    
    .back-button i {
        font-size: 0.9rem;
    }
`;
document.head.appendChild(backButtonStyles);
