/**
 * Register Page JavaScript
 * JOKIPRO.ID - Game Boosting Services
 */

// Initialize register page
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // Check if user came from order page
    const urlParams = new URLSearchParams(window.location.search);
    const returnUrl = urlParams.get('return');
    
    if (returnUrl) {
        sessionStorage.setItem('returnUrl', returnUrl);
    }
});

/**
 * Handle registration
 */
async function handleRegister(e) {
    e.preventDefault();
    
    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;
    const phone = document.getElementById('phone').value;
    const whatsapp = document.getElementById('whatsapp').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const agreeTerms = document.getElementById('agree_terms').checked;
    
    // Validation
    if (!agreeTerms) {
        showNotification('Anda harus menyetujui syarat dan ketentuan', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showNotification('Password dan konfirmasi password tidak sama', 'error');
        return;
    }
    
    if (password.length < 6) {
        showNotification('Password minimal 6 karakter', 'error');
        return;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Mendaftar...';
    submitBtn.disabled = true;
    
    try {
        // Call register API
        const response = await fetch('api/auth/register.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                email: email,
                phone: phone,
                whatsapp: whatsapp || phone,
                password: password
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Store user data and session token
            const userData = {
                ...result.data.user,
                loginMethod: 'email',
                loginTime: new Date().toISOString()
            };
            
            sessionStorage.setItem('userData', JSON.stringify(userData));
            sessionStorage.setItem('sessionToken', result.data.session.token);
            
            showNotification('Registrasi berhasil! Mengalihkan...', 'success');
            
            // Redirect to return URL or order page
            setTimeout(() => {
                const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
                sessionStorage.removeItem('returnUrl');
                window.location.href = returnUrl;
            }, 1500);
        } else {
            showNotification(result.message || 'Registrasi gagal', 'error');
        }
    } catch (error) {
        console.error('Register error:', error);
        showNotification('Terjadi kesalahan saat mendaftar', 'error');
    } finally {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

/**
 * Register with Google
 */
function registerWithGoogle() {
    showNotification('Mengalihkan ke Google...', 'info');
    
    // Simulate Google registration
    setTimeout(() => {
        const userData = {
            email: '<EMAIL>',
            name: 'Google User',
            loginMethod: 'google',
            loginTime: new Date().toISOString()
        };
        
        sessionStorage.setItem('userData', JSON.stringify(userData));
        showNotification('Registrasi dengan Google berhasil!', 'success');
        
        setTimeout(() => {
            const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
            sessionStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
        }, 1500);
    }, 1000);
}

/**
 * Register with Facebook
 */
function registerWithFacebook() {
    showNotification('Mengalihkan ke Facebook...', 'info');
    
    // Simulate Facebook registration
    setTimeout(() => {
        const userData = {
            email: '<EMAIL>',
            name: 'Facebook User',
            loginMethod: 'facebook',
            loginTime: new Date().toISOString()
        };
        
        sessionStorage.setItem('userData', JSON.stringify(userData));
        showNotification('Registrasi dengan Facebook berhasil!', 'success');
        
        setTimeout(() => {
            const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
            sessionStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
        }, 1500);
    }, 1000);
}

/**
 * Continue as guest
 */
function continueAsGuest() {
    showNotification('Melanjutkan sebagai guest...', 'info');
    
    setTimeout(() => {
        const returnUrl = sessionStorage.getItem('returnUrl') || 'order.html';
        sessionStorage.removeItem('returnUrl');
        window.location.href = returnUrl;
    }, 1000);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
