// Order Page JavaScript

let currentStep = 1;
let selectedGame = null;
let selectedService = null;
let selectedPayment = null;
let orderData = {};

// Game data
const gameData = {
    'mobile-legends': {
        name: 'Mobile Legends',
        accountFields: [
            { id: 'gameId', label: 'Game ID', placeholder: '*********', required: true },
            { id: 'zoneId', label: 'Zone ID', placeholder: '1234', required: true }
        ],
        services: {
            'rank-push': {
                name: 'Rank Push',
                description: 'Naikkan rank dari tier saat ini ke target tier',
                basePrice: 70000,
                options: [
                    { label: 'Warrior → Elite', price: 70000, duration: '2-3 hari' },
                    { label: 'Elite → Master', price: 120000, duration: '3-4 hari' },
                    { label: 'Master → Grandmaster', price: 180000, duration: '4-5 hari' },
                    { label: 'GM → Epic', price: 250000, duration: '5-6 hari' },
                    { label: 'Epic → Legend', price: 350000, duration: '6-7 hari' },
                    { label: 'Legend → Mythic', price: 500000, duration: '7-10 hari' }
                ]
            },
            'hero-mmr': {
                name: 'Hero MMR Boost',
                description: 'Naikkan MMR hero spesifik',
                basePrice: 15000,
                options: [
                    { label: 'Per 100 MMR', price: 15000, duration: '1-2 hari' },
                    { label: 'Per 500 MMR', price: 65000, duration: '3-5 hari' },
                    { label: 'Top Local', price: 200000, duration: '7-10 hari' }
                ]
            },
            'win-streak': {
                name: 'Win Streak',
                description: 'Dapatkan win streak sesuai target',
                basePrice: 10000,
                options: [
                    { label: '5 Win Streak', price: 50000, duration: '1-2 hari' },
                    { label: '10 Win Streak', price: 90000, duration: '2-3 hari' },
                    { label: '15 Win Streak', price: 130000, duration: '3-4 hari' },
                    { label: '20+ Win Streak', price: 180000, duration: '4-5 hari' }
                ]
            },
            'classic-match': {
                name: 'Classic Match',
                description: 'Win rate boost untuk classic match',
                basePrice: 8000,
                options: [
                    { label: '10 Classic Wins', price: 40000, duration: '1-2 hari' },
                    { label: '25 Classic Wins', price: 90000, duration: '2-3 hari' },
                    { label: '50 Classic Wins', price: 170000, duration: '4-5 hari' }
                ]
            },
            'event-completion': {
                name: 'Event Completion',
                description: 'Selesaikan event dan dapatkan reward',
                basePrice: 25000,
                options: [
                    { label: 'Weekly Event', price: 25000, duration: '1-3 hari' },
                    { label: 'Monthly Event', price: 75000, duration: '5-7 hari' },
                    { label: 'Special Event', price: 150000, duration: '7-14 hari' }
                ]
            }
        }
    },
    'pubg-mobile': {
        name: 'PUBG Mobile',
        accountFields: [
            { id: 'gameId', label: 'Game ID', placeholder: '*********', required: true }
        ],
        services: {
            'rank-push': {
                name: 'Rank Push',
                description: 'Naikkan rank dari tier saat ini ke target tier',
                basePrice: 50000,
                options: [
                    { label: 'Bronze → Silver', price: 50000, duration: '2-3 hari' },
                    { label: 'Silver → Gold', price: 80000, duration: '3-4 hari' },
                    { label: 'Gold → Platinum', price: 120000, duration: '4-5 hari' },
                    { label: 'Platinum → Diamond', price: 180000, duration: '5-7 hari' },
                    { label: 'Diamond → Crown', price: 250000, duration: '7-10 hari' },
                    { label: 'Crown → Ace', price: 350000, duration: '10-14 hari' }
                ]
            },
            'kd-boost': {
                name: 'K/D Boost',
                description: 'Naikkan K/D ratio sesuai target',
                basePrice: 30000,
                options: [
                    { label: 'Target K/D 2.0', price: 30000, duration: '2-3 hari' },
                    { label: 'Target K/D 3.0', price: 60000, duration: '4-5 hari' },
                    { label: 'Target K/D 4.0+', price: 100000, duration: '7-10 hari' }
                ]
            },
            'conqueror-push': {
                name: 'Conqueror Push',
                description: 'Push ke rank Conqueror (Top 500)',
                basePrice: 500000,
                options: [
                    { label: 'Ace → Conqueror', price: 500000, duration: '14-21 hari' },
                    { label: 'Maintain Conqueror', price: 300000, duration: '7-14 hari' }
                ]
            },
            'rp-missions': {
                name: 'RP Missions',
                description: 'Selesaikan misi RP dan dapatkan reward',
                basePrice: 25000,
                options: [
                    { label: 'Weekly RP Missions', price: 25000, duration: '1-3 hari' },
                    { label: 'Season Pass Complete', price: 150000, duration: '7-14 hari' },
                    { label: 'Elite Pass Complete', price: 200000, duration: '10-21 hari' }
                ]
            }
        }
    },
    'point-blank': {
        name: 'Point Blank',
        accountFields: [
            { id: 'username', label: 'Username', placeholder: 'username123', required: true }
        ],
        services: {
            'rank-joki': {
                name: 'Joki Pangkat',
                description: 'Naikkan pangkat sesuai target',
                basePrice: 50000,
                options: [
                    { label: 'Letnan → Kapten', price: 50000, duration: '2-3 hari' },
                    { label: 'Kapten → Mayor', price: 75000, duration: '3-4 hari' },
                    { label: 'Mayor → Kolonel', price: 100000, duration: '4-5 hari' },
                    { label: 'Kolonel → Jendral', price: 150000, duration: '5-7 hari' }
                ]
            },
            'kd-ratio': {
                name: 'K/D Ratio',
                description: 'Naikkan K/D ratio sesuai target',
                basePrice: 20000,
                options: [
                    { label: 'Target K/D 1.5', price: 20000, duration: '2-3 hari' },
                    { label: 'Target K/D 2.0', price: 40000, duration: '3-4 hari' },
                    { label: 'Target K/D 3.0+', price: 100000, duration: '5-7 hari' }
                ]
            }
        }
    },
    'valorant': {
        name: 'Valorant',
        accountFields: [
            { id: 'riotId', label: 'Riot ID', placeholder: 'Username#TAG', required: true }
        ],
        services: {
            'rank-boost': {
                name: 'Rank Boost',
                description: 'Naikkan rank dari tier saat ini ke target tier',
                basePrice: 100000,
                options: [
                    { label: 'Iron → Bronze', price: 100000, duration: '3-4 hari' },
                    { label: 'Bronze → Silver', price: 150000, duration: '4-5 hari' },
                    { label: 'Silver → Gold', price: 200000, duration: '5-6 hari' },
                    { label: 'Gold → Platinum', price: 250000, duration: '6-8 hari' },
                    { label: 'Platinum → Diamond', price: 350000, duration: '8-10 hari' }
                ]
            },
            'rr-farming': {
                name: 'RR Farming',
                description: 'Farm RR (Rank Rating) untuk naik rank lebih cepat',
                basePrice: 50000,
                options: [
                    { label: '+50 RR', price: 50000, duration: '2-3 hari' },
                    { label: '+100 RR', price: 90000, duration: '3-4 hari' },
                    { label: '+200 RR', price: 170000, duration: '5-7 hari' }
                ]
            },
            'competitive-matches': {
                name: 'Competitive Matches',
                description: 'Win competitive matches dengan performance bagus',
                basePrice: 25000,
                options: [
                    { label: '5 Competitive Wins', price: 125000, duration: '2-3 hari' },
                    { label: '10 Competitive Wins', price: 230000, duration: '4-5 hari' },
                    { label: '20 Competitive Wins', price: 400000, duration: '7-10 hari' }
                ]
            },
            'placement-matches': {
                name: 'Placement Matches',
                description: 'Placement matches untuk rank terbaik di awal season',
                basePrice: 200000,
                options: [
                    { label: '5 Placement Matches', price: 200000, duration: '1-2 hari' },
                    { label: 'High Rank Placement', price: 350000, duration: '1-2 hari' }
                ]
            }
        }
    },
    'free-fire': {
        name: 'Free Fire',
        accountFields: [
            { id: 'gameId', label: 'User ID', placeholder: '*********', required: true }
        ],
        services: {
            'rank-push': {
                name: 'Rank Push',
                description: 'Naikkan rank dari tier saat ini ke target tier',
                basePrice: 40000,
                options: [
                    { label: 'Bronze → Silver', price: 40000, duration: '2-3 hari' },
                    { label: 'Silver → Gold', price: 60000, duration: '3-4 hari' },
                    { label: 'Gold → Platinum', price: 90000, duration: '4-5 hari' },
                    { label: 'Platinum → Diamond', price: 120000, duration: '5-6 hari' }
                ]
            },
            'kd-boost': {
                name: 'K/D Boost',
                description: 'Naikkan K/D ratio untuk statistik yang lebih baik',
                basePrice: 25000,
                options: [
                    { label: 'Target K/D 2.0', price: 25000, duration: '2-3 hari' },
                    { label: 'Target K/D 3.0', price: 45000, duration: '3-4 hari' },
                    { label: 'Target K/D 4.0+', price: 80000, duration: '5-7 hari' }
                ]
            },
            'booyah-achievement': {
                name: 'Booyah Achievement',
                description: 'Dapatkan Booyah (Victory) sesuai target',
                basePrice: 15000,
                options: [
                    { label: '10 Booyah', price: 30000, duration: '1-2 hari' },
                    { label: '25 Booyah', price: 70000, duration: '3-4 hari' },
                    { label: '50 Booyah', price: 130000, duration: '5-7 hari' }
                ]
            },
            'event-completion': {
                name: 'Event Completion',
                description: 'Selesaikan event dan dapatkan reward eksklusif',
                basePrice: 15000,
                options: [
                    { label: 'Weekly Event', price: 15000, duration: '1-3 hari' },
                    { label: 'Monthly Event', price: 45000, duration: '5-7 hari' },
                    { label: 'Special Event', price: 60000, duration: '7-10 hari' }
                ]
            }
        }
    },
    'genshin-impact': {
        name: 'Genshin Impact',
        accountFields: [
            { id: 'uid', label: 'UID', placeholder: '*********', required: true }
        ],
        services: {
            'ar-boost': {
                name: 'AR Boost',
                description: 'Naikkan Adventure Rank sesuai target',
                basePrice: 20000,
                options: [
                    { label: 'AR 1-20', price: 20000, duration: '1-2 hari' },
                    { label: 'AR 20-35', price: 40000, duration: '2-3 hari' },
                    { label: 'AR 35-45', price: 80000, duration: '3-5 hari' },
                    { label: 'AR 45-55', price: 150000, duration: '5-7 hari' }
                ]
            },
            'spiral-abyss': {
                name: 'Spiral Abyss',
                description: 'Clear Spiral Abyss dengan 36 bintang',
                basePrice: 80000,
                options: [
                    { label: 'Floor 9-10 (36★)', price: 80000, duration: '1-2 hari' },
                    { label: 'Floor 11-12 (36★)', price: 150000, duration: '2-3 hari' },
                    { label: 'Full Clear (36★)', price: 200000, duration: '2-3 hari' }
                ]
            },
            'daily-commissions': {
                name: 'Daily Commissions',
                description: 'Selesaikan daily commissions untuk primogems',
                basePrice: 5000,
                options: [
                    { label: '7 Days Commissions', price: 35000, duration: '7 hari' },
                    { label: '14 Days Commissions', price: 65000, duration: '14 hari' },
                    { label: '30 Days Commissions', price: 120000, duration: '30 hari' }
                ]
            },
            'world-quests': {
                name: 'World Quests',
                description: 'Selesaikan world quest dan exploration',
                basePrice: 30000,
                options: [
                    { label: 'Mondstadt Quests', price: 60000, duration: '3-5 hari' },
                    { label: 'Liyue Quests', price: 80000, duration: '4-6 hari' },
                    { label: 'Inazuma Quests', price: 100000, duration: '5-7 hari' },
                    { label: 'All Regions Complete', price: 200000, duration: '10-14 hari' }
                ]
            }
        }
    }
};

document.addEventListener('DOMContentLoaded', function() {
    checkLoginStatus();
    setupGameSelection();
    setupPaymentSelection();
    setupTermsAgreement();
    autoFillFromURL();
});

function setupGameSelection() {
    const gameOptions = document.querySelectorAll('.game-option');
    
    gameOptions.forEach(option => {
        option.addEventListener('click', function() {
            gameOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedGame = this.dataset.game;
            setupServiceSelection(); // Add this line to populate services
            updateSummary();
            document.querySelector('#step-1 .btn-next').disabled = false;
        });
    });
}

function setupServiceSelection() {
    if (!selectedGame || !gameData[selectedGame]) return;
    
    const serviceContainer = document.getElementById('service-options');
    const services = gameData[selectedGame].services;
    
    serviceContainer.innerHTML = '';
    
    Object.keys(services).forEach(serviceKey => {
        const service = services[serviceKey];
        
        const serviceElement = document.createElement('div');
        serviceElement.className = 'service-option';
        serviceElement.dataset.service = serviceKey;
        
        serviceElement.innerHTML = `
            <h4>${service.name}</h4>
            <p>${service.description}</p>
            <div class="service-price">Mulai dari Rp ${service.basePrice.toLocaleString('id-ID')}</div>
        `;
        
        serviceElement.addEventListener('click', function() {
            document.querySelectorAll('.service-option').forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedService = serviceKey;
            updateSummary();
            document.querySelector('#step-2 .btn-next').disabled = false;
        });
        
        serviceContainer.appendChild(serviceElement);
    });
}

function setupAccountFields() {
    if (!selectedGame || !gameData[selectedGame]) return;
    
    const accountFieldsContainer = document.getElementById('account-fields');
    const fields = gameData[selectedGame].accountFields;
    
    accountFieldsContainer.innerHTML = '';
    
    const formRow = document.createElement('div');
    formRow.className = 'form-row';
    
    fields.forEach(field => {
        const formGroup = document.createElement('div');
        formGroup.className = 'form-group';
        
        formGroup.innerHTML = `
            <label for="${field.id}">${field.label} ${field.required ? '*' : ''}</label>
            <input type="text" id="${field.id}" name="${field.id}" 
                   placeholder="${field.placeholder}" ${field.required ? 'required' : ''}>
        `;
        
        formRow.appendChild(formGroup);
    });
    
    accountFieldsContainer.appendChild(formRow);
}

function setupServiceDetails() {
    if (!selectedGame || !selectedService || !gameData[selectedGame]) return;
    
    const serviceDetailsContainer = document.getElementById('service-details');
    const service = gameData[selectedGame].services[selectedService];
    
    if (!service) return;
    
    serviceDetailsContainer.innerHTML = `
        <div class="form-group">
            <label for="serviceOption">Pilih Detail Layanan *</label>
            <select id="serviceOption" name="serviceOption" required>
                <option value="">Pilih detail layanan</option>
                ${service.options.map(option => 
                    `<option value="${option.label}" data-price="${option.price}" data-duration="${option.duration}">
                        ${option.label} - Rp ${option.price.toLocaleString('id-ID')} (${option.duration})
                    </option>`
                ).join('')}
            </select>
        </div>
    `;
    
    document.getElementById('serviceOption').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            orderData.price = parseInt(selectedOption.dataset.price);
            orderData.duration = selectedOption.dataset.duration;
            orderData.serviceDetail = selectedOption.value;
            updateSummary();
        }
    });
}

function setupPaymentSelection() {
    const paymentOptions = document.querySelectorAll('.payment-option');
    
    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            paymentOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedPayment = this.dataset.method;
            checkSubmitButton();
        });
    });
}

function setupTermsAgreement() {
    const agreeTerms = document.getElementById('agreeTerms');
    agreeTerms.addEventListener('change', function() {
        checkSubmitButton();
    });
}

function checkSubmitButton() {
    const agreeTerms = document.getElementById('agreeTerms');
    const submitBtn = document.querySelector('.btn-submit');
    
    if (selectedPayment && agreeTerms.checked) {
        submitBtn.disabled = false;
    } else {
        submitBtn.disabled = true;
    }
}

function nextStep() {
    if (currentStep < 4) {
        document.getElementById(`step-${currentStep}`).classList.remove('active');
        document.querySelector(`[data-step="${currentStep}"]`).classList.remove('active');
        
        currentStep++;
        
        document.getElementById(`step-${currentStep}`).classList.add('active');
        document.querySelector(`[data-step="${currentStep}"]`).classList.add('active');
        
        if (currentStep === 2) {
            setupServiceSelection();
        } else if (currentStep === 3) {
            setupAccountFields();
            setupServiceDetails();
        }
        
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

function prevStep() {
    if (currentStep > 1) {
        document.getElementById(`step-${currentStep}`).classList.remove('active');
        document.querySelector(`[data-step="${currentStep}"]`).classList.remove('active');
        
        currentStep--;
        
        document.getElementById(`step-${currentStep}`).classList.add('active');
        document.querySelector(`[data-step="${currentStep}"]`).classList.add('active');
        
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

function updateSummary() {
    document.getElementById('summary-game').textContent = selectedGame ? gameData[selectedGame].name : '-';
    document.getElementById('summary-service').textContent = selectedService && selectedGame ? 
        gameData[selectedGame].services[selectedService].name : '-';
    document.getElementById('summary-details').textContent = orderData.serviceDetail || '-';
    document.getElementById('summary-duration').textContent = orderData.duration || '-';
    document.getElementById('summary-price').textContent = orderData.price ? 
        `Rp ${orderData.price.toLocaleString('id-ID')}` : 'Rp 0';
}

function submitOrder() {
    const formData = new FormData(document.getElementById('orderDetailsForm'));
    const customerData = Object.fromEntries(formData);
    
    if (!validateOrderForm(customerData)) {
        return;
    }
    
    const finalOrderData = {
        game: selectedGame,
        service: selectedService,
        serviceDetail: orderData.serviceDetail,
        price: orderData.price,
        duration: orderData.duration,
        payment: selectedPayment,
        customer: customerData,
        timestamp: new Date().toISOString()
    };
    
    const submitBtn = document.querySelector('.btn-submit');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        const whatsappMessage = createOrderWhatsAppMessage(finalOrderData);
        window.open(`https://wa.me/*************?text=${encodeURIComponent(whatsappMessage)}`, '_blank');
        
        alert('Pesanan berhasil dibuat! Anda akan diarahkan ke WhatsApp untuk konfirmasi pembayaran.');
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
}

function validateOrderForm(data) {
    const required = ['customerName', 'customerEmail', 'customerPhone', 'accountPassword'];
    
    for (let field of required) {
        if (!data[field] || data[field].trim() === '') {
            alert(`Field ${field} harus diisi!`);
            return false;
        }
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.customerEmail)) {
        alert('Format email tidak valid!');
        return false;
    }
    
    return true;
}

function createOrderWhatsAppMessage(orderData) {
    return `🎮 *PESANAN BARU JOKIPRO.ID* 🎮

📋 *Detail Pesanan:*
• Game: ${gameData[orderData.game].name}
• Layanan: ${gameData[orderData.game].services[orderData.service].name}
• Detail: ${orderData.serviceDetail}
• Harga: Rp ${orderData.price.toLocaleString('id-ID')}
• Estimasi: ${orderData.duration}

👤 *Data Customer:*
• Nama: ${orderData.customer.customerName}
• Email: ${orderData.customer.customerEmail}
• WhatsApp: ${orderData.customer.customerPhone}

💳 *Pembayaran:*
• Metode: ${orderData.payment.toUpperCase()}
• Total: Rp ${orderData.price.toLocaleString('id-ID')}
• DP 50%: Rp ${(orderData.price / 2).toLocaleString('id-ID')}

📝 *Catatan:*
${orderData.customer.specialNotes || 'Tidak ada catatan khusus'}

Mohon konfirmasi pesanan dan kirim detail pembayaran.

Terima kasih! 🙏`;
}

function autoFillFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.get('game')) {
        const gameElement = document.querySelector(`[data-game="${urlParams.get('game')}"]`);
        if (gameElement) {
            gameElement.click();
        }
    }
}

window.OrderUtils = {
    nextStep,
    prevStep,
    submitOrder,
    updateSummary
};

/**
 * Check login status and update UI
 */
function checkLoginStatus() {
    const userData = sessionStorage.getItem('userData') || localStorage.getItem('userData');
    const loginStatus = document.getElementById('loginStatus');

    if (!loginStatus) return;

    if (userData) {
        const user = JSON.parse(userData);

        // Show user info
        loginStatus.innerHTML = `
            <div class="user-info">
                <i class="fas fa-user-check"></i>
                <div>
                    <h3>Selamat datang, ${user.name}!</h3>
                    <p>Login via ${user.loginMethod} • ${user.email}</p>
                </div>
                <a href="#" onclick="logout()" class="btn-logout">Logout</a>
            </div>
        `;

        // Pre-fill email if available
        const emailInput = document.getElementById('email');
        if (emailInput && user.email !== '<EMAIL>') {
            emailInput.value = user.email;
        }

        // Pre-fill name if available
        const nameInput = document.getElementById('name');
        if (nameInput) {
            nameInput.value = user.name;
        }
    } else {
        // Show login prompt
        loginStatus.innerHTML = `
            <div class="login-prompt">
                <div class="login-prompt-content">
                    <i class="fas fa-user-circle"></i>
                    <div>
                        <h3>Masuk untuk Order Lebih Mudah</h3>
                        <p>Login untuk menyimpan data dan tracking order</p>
                    </div>
                    <a href="login.html?return=order.html" class="btn-login-prompt">Login</a>
                </div>
            </div>
        `;
    }
}

/**
 * Logout function
 */
function logout() {
    sessionStorage.removeItem('userData');
    localStorage.removeItem('userData');
    checkLoginStatus();

    // Clear pre-filled data
    const emailInput = document.getElementById('email');
    const nameInput = document.getElementById('name');

    if (emailInput) emailInput.value = '';
    if (nameInput) nameInput.value = '';

    showNotification('Logout berhasil', 'success');
}
